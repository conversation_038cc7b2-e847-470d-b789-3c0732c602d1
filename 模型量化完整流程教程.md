# 模型量化完整流程教程：从载入到校准到量化

## 📋 目录
1. [环境准备与依赖](#环境准备与依赖)
2. [模型载入流程](#模型载入流程)
3. [数据准备与校准](#数据准备与校准)
4. [量化配置详解](#量化配置详解)
5. [量化执行流程](#量化执行流程)
6. [参数配置指南](#参数配置指南)
7. [完整代码示例](#完整代码示例)

## 🔧 环境准备与依赖

### 关键版本要求
```bash
# 核心依赖 - 必须使用兼容版本
torch==2.5.1+cu124          # 避免FX tracing问题
torchvision==0.20.1+cu124   # 匹配torch版本
transformers>=4.52.0        # 支持最新模型
datasets>=2.0.0             # 数据集处理
llmcompressor               # 量化框架
```

### 环境验证代码
```python
import torch
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")
print(f"GPU数量: {torch.cuda.device_count()}")

# 验证关键导入
from llmcompressor import oneshot
from llmcompressor.modifiers.quantization import GPTQModifier
from llmcompressor.modifiers.smoothquant import SmoothQuantModifier
print("✅ 所有依赖导入成功")
```

## 📥 模型载入流程

### 步骤1: 基础模型加载
```python
from transformers import AutoTokenizer, AutoModelForCausalLM

# 模型路径配置
MODEL_ID = "/home/<USER>/single_llama"  # 本地路径
# MODEL_ID = "meta-llama/Llama-2-7b-hf"  # HuggingFace路径

# 加载模型 - 关键参数解释
model = AutoModelForCausalLM.from_pretrained(
    MODEL_ID,
    device_map="auto",        # 自动分配GPU/CPU
    torch_dtype="auto",       # 自动选择数据类型(通常FP16)
    trust_remote_code=True,   # 允许自定义代码(如需要)
    low_cpu_mem_usage=True,   # 降低CPU内存使用
)

# 加载分词器
tokenizer = AutoTokenizer.from_pretrained(MODEL_ID)
```

### 步骤2: 模型配置检查
```python
# 检查模型配置
print("模型配置信息:")
print(f"模型类型: {type(model).__name__}")
print(f"配置: {model.config}")

# 统计参数
total_params = sum(p.numel() for p in model.parameters())
print(f"总参数数: {total_params:,}")

# 检查模型结构
print("\n关键层结构:")
for name, module in model.named_modules():
    if isinstance(module, torch.nn.Linear):
        print(f"Linear层: {name} -> [{module.in_features}, {module.out_features}]")
```

### 步骤3: Tokenizer配置
```python
# 处理特殊token - 重要步骤！
if tokenizer.pad_token is None:
    tokenizer.add_special_tokens({'pad_token': '[PAD]'})
    # 调整模型词汇表大小
    model.resize_token_embeddings(len(tokenizer))
    print(f"✅ 添加pad_token，词汇表大小: {len(tokenizer)}")

# 检查tokenizer配置
print(f"词汇表大小: {len(tokenizer)}")
print(f"特殊token: {tokenizer.special_tokens_map}")
```

## 📊 数据准备与校准

### 步骤1: 数据集加载
```python
from datasets import load_dataset

# 数据集配置参数
NUM_CALIBRATION_SAMPLES = 512    # 校准样本数量
MAX_SEQUENCE_LENGTH = 512        # 最大序列长度
DATASET_PATH = "/workspace/dataset/datasets--HuggingFaceH4--ultrachat_200k/"

# 加载数据集
ds = load_dataset(DATASET_PATH, split="train_sft")
print(f"原始数据集大小: {len(ds)}")

# 随机选择校准样本
ds = ds.shuffle(seed=42).select(range(NUM_CALIBRATION_SAMPLES))
print(f"校准样本数量: {len(ds)}")
```

### 步骤2: 数据预处理
```python
# 预处理函数 - 应用chat template
def preprocess(example):
    """将对话格式转换为文本"""
    text = tokenizer.apply_chat_template(
        example["messages"], 
        tokenize=False,
        add_generation_prompt=False
    )
    return {"text": text}

# 应用预处理
ds = ds.map(preprocess)
print("✅ Chat template应用完成")

# 查看预处理结果
sample_text = ds[0]['text']
print(f"样本文本长度: {len(sample_text)} 字符")
print(f"文本预览: {sample_text[:200]}...")
```

### 步骤3: Tokenization
```python
def tokenize(sample):
    """文本tokenization"""
    return tokenizer(
        sample["text"],
        padding=True,              # 填充到相同长度
        max_length=MAX_SEQUENCE_LENGTH,  # 最大长度
        truncation=True,           # 截断超长序列
        add_special_tokens=False,  # 不添加特殊token
        return_tensors=None        # 返回Python list
    )

# 应用tokenization
ds = ds.map(tokenize, remove_columns=ds.column_names)
print("✅ Tokenization完成")

# 检查token统计
token_lengths = [len(sample['input_ids']) for sample in ds]
print(f"Token长度统计:")
print(f"  平均: {sum(token_lengths)/len(token_lengths):.1f}")
print(f"  最小: {min(token_lengths)}")
print(f"  最大: {max(token_lengths)}")
```

## ⚙️ 量化配置详解

### SmoothQuantModifier配置
```python
from llmcompressor.modifiers.smoothquant import SmoothQuantModifier

# SmoothQuant配置参数详解
smoothquant_config = SmoothQuantModifier(
    smoothing_strength=0.8,      # 平滑强度 [0.0-1.0]
    mappings=None,               # 自动推断映射关系
    num_calibration_steps=None,  # 使用全部校准数据
    targets=None,                # 自动识别目标层
)

print("SmoothQuant配置:")
print(f"  平滑强度: {smoothquant_config.smoothing_strength}")
print(f"  映射关系: {smoothquant_config.mappings}")
```

### GPTQModifier配置
```python
from llmcompressor.modifiers.quantization import GPTQModifier

# GPTQ配置参数详解
gptq_config = GPTQModifier(
    targets="Linear",            # 目标层类型
    scheme="W8A8",              # 量化方案: W8A8, W4A16, W8A16等
    ignore=["lm_head"],         # 忽略的层
    dampening_frac=0.01,        # 阻尼系数
    block_size=128,             # 块大小
    sequential_update=False,     # 是否顺序更新
    percdamp=0.01,              # 百分比阻尼
)

print("GPTQ配置:")
print(f"  目标层: {gptq_config.targets}")
print(f"  量化方案: {gptq_config.scheme}")
print(f"  忽略层: {gptq_config.ignore}")
```

### 量化Recipe组合
```python
# 创建量化recipe - 顺序很重要！
recipe = [
    smoothquant_config,  # 第一步: SmoothQuant预处理
    gptq_config,        # 第二步: GPTQ量化
]

print("量化Recipe:")
for i, modifier in enumerate(recipe, 1):
    print(f"  {i}. {type(modifier).__name__}")
```

## 🚀 量化执行流程

### oneshot量化函数详解
```python
from llmcompressor import oneshot

# oneshot函数参数详解
quantized_model = oneshot(
    model=model,                    # 待量化模型
    dataset=ds,                     # 校准数据集
    recipe=recipe,                  # 量化配置
    max_seq_length=MAX_SEQUENCE_LENGTH,     # 最大序列长度
    num_calibration_samples=NUM_CALIBRATION_SAMPLES,  # 校准样本数
    output_dir="quantized_model",   # 输出目录(可选)
    save_compressed=True,           # 保存压缩格式
    do_save=True,                  # 是否保存模型
)
```

### 量化过程监控
```python
import logging

# 设置日志级别查看详细过程
logging.getLogger("llmcompressor").setLevel(logging.INFO)

# 量化过程会显示:
# 1. SmoothQuant阶段
#    - Preparing cache: 准备缓存
#    - Calibrating: 校准激活统计
#    - Propagating: 传播平滑效果
#
# 2. GPTQ阶段  
#    - Quantizing layer: 逐层量化
#    - Compression metrics: 压缩指标
#    - Error monitoring: 误差监控
```

## 📋 参数配置指南

### 关键参数选择建议

#### 1. 校准数据配置
```python
# 校准样本数量选择
NUM_CALIBRATION_SAMPLES = {
    "快速测试": 32,      # 开发调试
    "标准配置": 512,     # 一般使用  
    "高精度": 1024,      # 追求精度
}

# 序列长度配置
MAX_SEQUENCE_LENGTH = {
    "短文本": 256,       # 对话、问答
    "标准": 512,         # 通用场景
    "长文本": 1024,      # 文档处理
}
```

#### 2. SmoothQuant参数
```python
# 平滑强度选择
smoothing_strength = {
    0.5: "轻度平滑",     # 保守设置
    0.8: "标准平滑",     # 推荐设置
    1.0: "最大平滑",     # 激进设置
}
```

#### 3. GPTQ量化方案
```python
# 量化方案选择
quantization_schemes = {
    "W8A8": "权重8位+激活8位",    # 最大压缩
    "W8A16": "权重8位+激活16位",  # 平衡方案
    "W4A16": "权重4位+激活16位",  # 极致压缩
}
```

### 性能vs精度权衡
```python
# 配置组合建议
configs = {
    "高性能": {
        "scheme": "W8A8",
        "smoothing_strength": 0.8,
        "samples": 512,
    },
    "高精度": {
        "scheme": "W8A16", 
        "smoothing_strength": 0.5,
        "samples": 1024,
    },
    "极致压缩": {
        "scheme": "W4A16",
        "smoothing_strength": 1.0,
        "samples": 1024,
    }
}
```

## 💾 模型保存与验证

### 保存量化模型
```python
# 保存模型和tokenizer
SAVE_DIR = "quantized_model_output"
model.save_pretrained(SAVE_DIR, save_compressed=True)
tokenizer.save_pretrained(SAVE_DIR)

# 检查保存的文件
import os
print("保存的文件:")
for file in os.listdir(SAVE_DIR):
    file_path = os.path.join(SAVE_DIR, file)
    size_mb = os.path.getsize(file_path) / 1024**2
    print(f"  {file}: {size_mb:.2f}MB")
```

### 量化效果验证
```python
# 加载量化模型进行验证
quantized_model = AutoModelForCausalLM.from_pretrained(SAVE_DIR)

# 简单推理测试
test_input = "Hello, how are you?"
inputs = tokenizer(test_input, return_tensors="pt")

with torch.no_grad():
    outputs = quantized_model(**inputs)
    
print("✅ 量化模型推理成功")
```

## 🎯 实际代码运行流程总结

基于debug代码运行的实际结果，以下是完整的量化流程：

### 实际运行时序
```
步骤1: 环境验证 (0.1s)
├── PyTorch 2.5.1+cu124 ✅
├── 8x NVIDIA A100-SXM4-40GB ✅
└── 依赖导入成功 ✅

步骤2: 模型加载 (0.30s)
├── LlamaForCausalLM (68,144参数)
├── 8个Linear层识别
├── pad_token添加 (32000→32001)
└── 模型大小: 0.13MB (FP16)

步骤3: 数据准备 (1.2s)
├── UltraChat-200k数据集
├── 32个校准样本选择
├── Chat template应用
└── Tokenization (512 tokens/样本)

步骤4: 量化配置 (0.1s)
├── SmoothQuant (强度0.8)
├── GPTQ (W8A8方案)
└── Recipe创建

步骤5: 量化执行 (6.96s)
├── SmoothQuant预处理 (3.5s)
│   ├── 激活统计收集
│   ├── 平滑因子计算
│   └── LayerNorm调整
└── GPTQ量化 (3.5s)
    ├── 逐层权重量化
    ├── 量化参数生成
    └── 误差监控 (≈0.00)

步骤6: 结果保存 (0.5s)
├── 量化模型: 2.03MB
├── 配置文件: recipe.yaml
└── 7个量化参数层
```

### 关键观察结果

#### 1. 量化过程详细日志
```
SmoothQuant阶段:
- Preparing cache: 缓存准备
- (1/2) Calibrating: 第一轮校准
- Smoothing with input_layernorm: 输入层归一化平滑
- Smoothing with post_attention_layernorm: 注意力后归一化平滑
- (2/2) Calibrating: 第二轮校准

GPTQ阶段:
- Quantizing q_proj: 0.08s, error 0.00
- Quantizing k_proj: 0.01s, error 0.00
- Quantizing v_proj: 0.01s, error 0.00
- Quantizing o_proj: 0.33s, error 0.00
- Quantizing gate_proj: 0.01s, error 0.00
- Quantizing up_proj: 0.01s, error 0.00
- Quantizing down_proj: 0.04s, error 0.00
```

#### 2. 自动映射推断结果
```
系统自动识别的映射关系:
input_layernorm → q_proj, k_proj, v_proj
post_attention_layernorm → gate_proj, up_proj
```

#### 3. 量化参数生成
每个量化层生成：
- `weight_scale`: 缩放因子 (FP16)
- `weight_zero_point`: 零点偏移 (INT8)
- 原始权重转换为INT8格式

## 📋 参数配置最佳实践

### 核心参数配置表
| 参数类别 | 参数名 | 推荐值 | 说明 |
|---------|--------|--------|------|
| **模型加载** | device_map | "auto" | 自动GPU分配 |
| | torch_dtype | "auto" | 自动数据类型 |
| | low_cpu_mem_usage | True | 降低CPU内存 |
| **数据配置** | num_calibration_samples | 32-1024 | 校准样本数 |
| | max_sequence_length | 512 | 序列最大长度 |
| **SmoothQuant** | smoothing_strength | 0.8 | 平滑强度 |
| | mappings | None | 自动推断 |
| **GPTQ** | scheme | "W8A8" | 量化方案 |
| | targets | "Linear" | 目标层类型 |
| | ignore | ["lm_head"] | 忽略层 |
| | dampening_frac | 0.01 | 阻尼系数 |

### 不同场景的配置建议

#### 快速测试配置
```python
config = {
    "num_calibration_samples": 32,
    "max_sequence_length": 256,
    "smoothing_strength": 0.8,
    "scheme": "W8A8"
}
```

#### 生产环境配置
```python
config = {
    "num_calibration_samples": 512,
    "max_sequence_length": 512,
    "smoothing_strength": 0.8,
    "scheme": "W8A8"
}
```

#### 高精度配置
```python
config = {
    "num_calibration_samples": 1024,
    "max_sequence_length": 1024,
    "smoothing_strength": 0.5,
    "scheme": "W8A16"
}
```

## 🔧 故障排除指南

### 常见问题及解决方案

1. **FX Tracing错误**
   ```
   解决方案: 使用PyTorch 2.5.1+
   pip install torch==2.5.1+cu124
   ```

2. **内存不足**
   ```
   解决方案: 减少校准样本或序列长度
   num_calibration_samples = 32
   max_sequence_length = 256
   ```

3. **量化精度下降**
   ```
   解决方案: 调整平滑强度或使用更高精度方案
   smoothing_strength = 0.5  # 更保守
   scheme = "W8A16"  # 更高精度
   ```

这个教程涵盖了从模型载入到量化完成的完整流程，每个步骤都有详细的参数说明和配置建议。
