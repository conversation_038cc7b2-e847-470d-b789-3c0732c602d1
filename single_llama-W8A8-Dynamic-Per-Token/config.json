{"architectures": ["LlamaForCausalLM"], "attention_bias": false, "attention_dropout": 0.0, "bos_token_id": 1, "eos_token_id": 2, "head_dim": 32, "hidden_act": "silu", "hidden_size": 16, "initializer_range": 0.02, "intermediate_size": 32, "max_position_embeddings": 4096, "mlp_bias": false, "model_type": "llama", "num_attention_heads": 32, "num_hidden_layers": 1, "num_key_value_heads": 32, "pretraining_tp": 1, "quantization_config": {"config_groups": {"group_0": {"input_activations": {"actorder": null, "block_structure": null, "dynamic": true, "group_size": null, "num_bits": 8, "observer": null, "observer_kwargs": {}, "strategy": "token", "symmetric": true, "type": "int"}, "output_activations": null, "targets": ["Linear"], "weights": {"actorder": null, "block_structure": null, "dynamic": false, "group_size": null, "num_bits": 8, "observer": "minmax", "observer_kwargs": {}, "strategy": "channel", "symmetric": true, "type": "int"}}}, "format": "int-quantized", "global_compression_ratio": null, "ignore": ["lm_head"], "kv_cache_scheme": null, "quant_method": "compressed-tensors", "quantization_status": "compressed"}, "rms_norm_eps": 1e-05, "rope_scaling": null, "rope_theta": 10000.0, "tie_word_embeddings": false, "torch_dtype": "float16", "transformers_version": "4.52.0", "use_cache": true, "vocab_size": 32001}