2025-08-07 09:33:33.508 | DEBUG    | llmcompressor.core.lifecycle:reset:61 - Resetting compression lifecycle
2025-08-07 09:33:33.508 | INFO     | llmcompressor.core.lifecycle:reset:73 - Compression lifecycle reset
2025-08-07 09:33:33.509 | DEBUG    | llmcompressor.core.state:update:182 - Updating state with provided parameters: {'model': LlamaForCausalLM(
  (model): LlamaModel(
    (embed_tokens): Embedding(32001, 16)
    (layers): ModuleList(
      (0): LlamaDecoderLayer(
        (self_attn): LlamaAttention(
          (q_proj): Linear(in_features=16, out_features=1024, bias=False)
          (k_proj): Linear(in_features=16, out_features=1024, bias=False)
          (v_proj): Linear(in_features=16, out_features=1024, bias=False)
          (o_proj): Linear(in_features=1024, out_features=16, bias=False)
        )
        (mlp): LlamaMLP(
          (gate_proj): Linear(in_features=16, out_features=32, bias=False)
          (up_proj): Linear(in_features=16, out_features=32, bias=False)
          (down_proj): Linear(in_features=32, out_features=16, bias=False)
          (act_fn): SiLU()
        )
        (input_layernorm): LlamaRMSNorm((16,), eps=1e-05)
        (post_attention_layernorm): LlamaRMSNorm((16,), eps=1e-05)
      )
    )
    (norm): LlamaRMSNorm((16,), eps=1e-05)
    (rotary_emb): LlamaRotaryEmbedding()
  )
  (lm_head): Linear(in_features=16, out_features=32001, bias=False)
), 'teacher_model': None, 'optimizer': None, 'attach_optim_callbacks': True, 'train_data': None, 'val_data': None, 'test_data': None, 'calib_data': <torch.utils.data.dataloader.DataLoader object at 0x79879cc83f10>, 'copy_data': True, 'start': -1, 'steps_per_epoch': None, 'batches_per_step': None, 'loggers': None, 'model_log_cadence': None, 'kwargs': {}}
2025-08-07 09:33:33.512 | DEBUG    | llmcompressor.core.lifecycle:initialize:94 - Initializing compression lifecycle
2025-08-07 09:33:33.512 | INFO     | llmcompressor.recipe.recipe:from_modifiers:59 - Creating recipe from modifiers
2025-08-07 09:33:33.611 | INFO     | llmcompressor.modifiers.smoothquant.base:_infer_mappings_from_model:183 - No SmoothQuantModifier.mappings provided, inferring from model...
2025-08-07 09:33:33.635 | DEBUG    | llmcompressor.core.lifecycle:initialize:103 - Initialized modifier: modifiers=[SmoothQuantModifier(index=0, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=False, ended_=False, smoothing_strength=0.8, mappings=[LayerMap(balance_layers=['re:.*q_proj', 're:.*k_proj', 're:.*v_proj'], smooth_layers='re:.*input_layernorm'), LayerMap(balance_layers=['re:.*gate_proj', 're:.*up_proj'], smooth_layers='re:.*post_attention_layernorm')], ignore=[], num_calibration_steps=None, calibration_function=None), GPTQModifier(config_groups=None, targets=['Linear'], ignore=['lm_head'], scheme='W8A8', kv_cache_scheme=None, index=1, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=False, ended_=False, sequential_update=True, sequential_targets=None, block_size=128, dampening_frac=0.01, actorder=None, offload_hessians=False)] index=0 group='default' applied=False
2025-08-07 09:33:33.636 | INFO     | llmcompressor.core.lifecycle:initialize:108 - Compression lifecycle initialized for 1 modifiers
2025-08-07 09:33:33.636 | INFO     | llmcompressor.pipelines.independent.pipeline:IndependentPipeline:47 - Inferred `SequentialPipeline` for `SmoothQuantModifier`
2025-08-07 09:33:35.518 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-07 09:33:35.519 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if (input_ids is None) ^ (inputs_embeds is not None):
    raise ValueError('You must specify exactly one of input_ids or inputs_embeds')
2025-08-07 09:33:35.519 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-07 09:33:35.520 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-07 09:33:35.521 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if not isinstance(past_key_values, (type(None), Cache)):
    raise ValueError('The `past_key_values` should be either a `Cache` object or `None`.')
2025-08-07 09:33:35.521 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-07 09:33:35.522 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-07 09:33:35.522 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if inputs_embeds is None:
    inputs_embeds = self.embed_tokens(input_ids)
2025-08-07 09:33:35.522 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-07 09:33:35.523 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-07 09:33:35.523 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if use_cache and past_key_values is None:
    past_key_values = DynamicCache()
2025-08-07 09:33:35.523 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-07 09:33:35.524 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-07 09:33:35.525 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if cache_position is None:
    past_seen_tokens = past_key_values.get_seq_length() if past_key_values is not None else 0
    cache_position = torch.arange(past_seen_tokens, past_seen_tokens + inputs_embeds.shape[1], device=inputs_embeds.device)
2025-08-07 09:33:35.525 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-07 09:33:35.526 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-07 09:33:35.526 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if position_ids is None:
    position_ids = cache_position.unsqueeze(0)
2025-08-07 09:33:35.526 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-07 09:33:35.527 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:189 - ---- Autowrapper ----
2025-08-07 09:33:35.527 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:190 - self._update_causal_mask(attention_mask, inputs_embeds, cache_position, past_key_values, output_attentions)
2025-08-07 09:33:35.527 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:191 - ---------------------
2025-08-07 09:33:35.529 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-07 09:33:35.529 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if output_hidden_states:
    all_hidden_states += (hidden_states,)
2025-08-07 09:33:35.529 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-07 09:33:35.530 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-07 09:33:35.530 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if output_attentions:
    all_self_attns += (layer_outputs[1],)
2025-08-07 09:33:35.530 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-07 09:33:35.531 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-07 09:33:35.531 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if output_hidden_states:
    all_hidden_states += (hidden_states,)
2025-08-07 09:33:35.531 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-07 09:33:35.556 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-07 09:33:35.556 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if labels is not None:
    loss = self.loss_function(logits=logits, labels=labels, vocab_size=self.config.vocab_size, **kwargs)
2025-08-07 09:33:35.557 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-07 09:33:35.627 | DEBUG    | llmcompressor.core.lifecycle:event:191 - Handling event: EventType.CALIBRATION_EPOCH_START
2025-08-07 09:33:35.627 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - index=0 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False smoothing_strength=0.8 mappings=[LayerMap(balance_layers=['re:.*q_proj', 're:.*k_proj', 're:.*v_proj'], smooth_layers='re:.*input_layernorm'), LayerMap(balance_layers=['re:.*gate_proj', 're:.*up_proj'], smooth_layers='re:.*post_attention_layernorm')] ignore=[] num_calibration_steps=None calibration_function=None added <torch.utils.hooks.RemovableHandle object at 0x7987888b1a80>
2025-08-07 09:33:35.628 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - index=0 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False smoothing_strength=0.8 mappings=[LayerMap(balance_layers=['re:.*q_proj', 're:.*k_proj', 're:.*v_proj'], smooth_layers='re:.*input_layernorm'), LayerMap(balance_layers=['re:.*gate_proj', 're:.*up_proj'], smooth_layers='re:.*post_attention_layernorm')] ignore=[] num_calibration_steps=None calibration_function=None added <torch.utils.hooks.RemovableHandle object at 0x7987888b2080>
2025-08-07 09:33:35.628 | DEBUG    | llmcompressor.core.lifecycle:event:201 - Updated event with modifier: modifiers=[SmoothQuantModifier(index=0, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=True, ended_=False, smoothing_strength=0.8, mappings=[LayerMap(balance_layers=['re:.*q_proj', 're:.*k_proj', 're:.*v_proj'], smooth_layers='re:.*input_layernorm'), LayerMap(balance_layers=['re:.*gate_proj', 're:.*up_proj'], smooth_layers='re:.*post_attention_layernorm')], ignore=[], num_calibration_steps=None, calibration_function=None)] index=0 group='SmoothQuantModifier' applied=False
2025-08-07 09:33:39.589 | DEBUG    | llmcompressor.core.lifecycle:event:191 - Handling event: EventType.SEQUENTIAL_EPOCH_END
2025-08-07 09:33:39.590 | INFO     | llmcompressor.modifiers.smoothquant.base:_apply_smoothing:271 - Smoothing with model.layers.0.input_layernorm
2025-08-07 09:33:39.613 | INFO     | llmcompressor.modifiers.smoothquant.base:_apply_smoothing:271 - Smoothing with model.layers.0.post_attention_layernorm
2025-08-07 09:33:39.614 | DEBUG    | llmcompressor.core.lifecycle:event:201 - Updated event with modifier: modifiers=[SmoothQuantModifier(index=0, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=True, ended_=False, smoothing_strength=0.8, mappings=[LayerMap(balance_layers=['re:.*q_proj', 're:.*k_proj', 're:.*v_proj'], smooth_layers='re:.*input_layernorm'), LayerMap(balance_layers=['re:.*gate_proj', 're:.*up_proj'], smooth_layers='re:.*post_attention_layernorm')], ignore=[], num_calibration_steps=None, calibration_function=None)] index=0 group='SmoothQuantModifier' applied=False
2025-08-07 09:33:42.375 | DEBUG    | llmcompressor.core.lifecycle:event:191 - Handling event: EventType.SEQUENTIAL_EPOCH_END
2025-08-07 09:33:42.375 | DEBUG    | llmcompressor.core.lifecycle:event:201 - Updated event with modifier: modifiers=[SmoothQuantModifier(index=0, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=True, ended_=False, smoothing_strength=0.8, mappings=[LayerMap(balance_layers=['re:.*q_proj', 're:.*k_proj', 're:.*v_proj'], smooth_layers='re:.*input_layernorm'), LayerMap(balance_layers=['re:.*gate_proj', 're:.*up_proj'], smooth_layers='re:.*post_attention_layernorm')], ignore=[], num_calibration_steps=None, calibration_function=None)] index=0 group='SmoothQuantModifier' applied=False
2025-08-07 09:33:42.484 | DEBUG    | llmcompressor.core.lifecycle:event:191 - Handling event: EventType.CALIBRATION_EPOCH_END
2025-08-07 09:33:42.485 | DEBUG    | llmcompressor.core.lifecycle:event:201 - Updated event with modifier: modifiers=[SmoothQuantModifier(index=0, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=True, ended_=True, smoothing_strength=0.8, mappings=[LayerMap(balance_layers=['re:.*q_proj', 're:.*k_proj', 're:.*v_proj'], smooth_layers='re:.*input_layernorm'), LayerMap(balance_layers=['re:.*gate_proj', 're:.*up_proj'], smooth_layers='re:.*post_attention_layernorm')], ignore=[], num_calibration_steps=None, calibration_function=None)] index=0 group='SmoothQuantModifier' applied=False
2025-08-07 09:33:42.486 | INFO     | llmcompressor.pipelines.independent.pipeline:IndependentPipeline:47 - Inferred `SequentialPipeline` for `GPTQModifier`
2025-08-07 09:33:42.514 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-07 09:33:42.514 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if (input_ids is None) ^ (inputs_embeds is not None):
    raise ValueError('You must specify exactly one of input_ids or inputs_embeds')
2025-08-07 09:33:42.514 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-07 09:33:42.515 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-07 09:33:42.516 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if not isinstance(past_key_values, (type(None), Cache)):
    raise ValueError('The `past_key_values` should be either a `Cache` object or `None`.')
2025-08-07 09:33:42.516 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-07 09:33:42.517 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-07 09:33:42.517 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if inputs_embeds is None:
    inputs_embeds = self.embed_tokens(input_ids)
2025-08-07 09:33:42.517 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-07 09:33:42.518 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-07 09:33:42.518 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if use_cache and past_key_values is None:
    past_key_values = DynamicCache()
2025-08-07 09:33:42.518 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-07 09:33:42.519 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-07 09:33:42.519 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if cache_position is None:
    past_seen_tokens = past_key_values.get_seq_length() if past_key_values is not None else 0
    cache_position = torch.arange(past_seen_tokens, past_seen_tokens + inputs_embeds.shape[1], device=inputs_embeds.device)
2025-08-07 09:33:42.520 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-07 09:33:42.521 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-07 09:33:42.521 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if position_ids is None:
    position_ids = cache_position.unsqueeze(0)
2025-08-07 09:33:42.521 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-07 09:33:42.521 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:189 - ---- Autowrapper ----
2025-08-07 09:33:42.522 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:190 - self._update_causal_mask(attention_mask, inputs_embeds, cache_position, past_key_values, output_attentions)
2025-08-07 09:33:42.522 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:191 - ---------------------
2025-08-07 09:33:42.523 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-07 09:33:42.523 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if output_hidden_states:
    all_hidden_states += (hidden_states,)
2025-08-07 09:33:42.523 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-07 09:33:42.524 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-07 09:33:42.524 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if output_attentions:
    all_self_attns += (layer_outputs[1],)
2025-08-07 09:33:42.524 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-07 09:33:42.525 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-07 09:33:42.525 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if output_hidden_states:
    all_hidden_states += (hidden_states,)
2025-08-07 09:33:42.525 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-07 09:33:42.549 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-07 09:33:42.549 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if labels is not None:
    loss = self.loss_function(logits=logits, labels=labels, vocab_size=self.config.vocab_size, **kwargs)
2025-08-07 09:33:42.550 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-07 09:33:42.604 | DEBUG    | llmcompressor.core.lifecycle:event:191 - Handling event: EventType.CALIBRATION_EPOCH_START
2025-08-07 09:33:42.607 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - config_groups=None targets=['Linear'] ignore=['lm_head'] scheme='W8A8' kv_cache_scheme=None index=1 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False sequential_update=True sequential_targets=None block_size=128 dampening_frac=0.01 actorder=None offload_hessians=False added <torch.utils.hooks.RemovableHandle object at 0x79879d1fe7a0>
2025-08-07 09:33:42.607 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - config_groups=None targets=['Linear'] ignore=['lm_head'] scheme='W8A8' kv_cache_scheme=None index=1 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False sequential_update=True sequential_targets=None block_size=128 dampening_frac=0.01 actorder=None offload_hessians=False added <torch.utils.hooks.RemovableHandle object at 0x79879d1fe890>
2025-08-07 09:33:42.608 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - config_groups=None targets=['Linear'] ignore=['lm_head'] scheme='W8A8' kv_cache_scheme=None index=1 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False sequential_update=True sequential_targets=None block_size=128 dampening_frac=0.01 actorder=None offload_hessians=False added <torch.utils.hooks.RemovableHandle object at 0x79879d1fe770>
2025-08-07 09:33:42.608 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - config_groups=None targets=['Linear'] ignore=['lm_head'] scheme='W8A8' kv_cache_scheme=None index=1 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False sequential_update=True sequential_targets=None block_size=128 dampening_frac=0.01 actorder=None offload_hessians=False added <torch.utils.hooks.RemovableHandle object at 0x79879d1fe620>
2025-08-07 09:33:42.608 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - config_groups=None targets=['Linear'] ignore=['lm_head'] scheme='W8A8' kv_cache_scheme=None index=1 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False sequential_update=True sequential_targets=None block_size=128 dampening_frac=0.01 actorder=None offload_hessians=False added <torch.utils.hooks.RemovableHandle object at 0x79879d1fe6e0>
2025-08-07 09:33:42.609 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - config_groups=None targets=['Linear'] ignore=['lm_head'] scheme='W8A8' kv_cache_scheme=None index=1 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False sequential_update=True sequential_targets=None block_size=128 dampening_frac=0.01 actorder=None offload_hessians=False added <torch.utils.hooks.RemovableHandle object at 0x79879d1fe410>
2025-08-07 09:33:42.609 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - config_groups=None targets=['Linear'] ignore=['lm_head'] scheme='W8A8' kv_cache_scheme=None index=1 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False sequential_update=True sequential_targets=None block_size=128 dampening_frac=0.01 actorder=None offload_hessians=False added <torch.utils.hooks.RemovableHandle object at 0x79879d1fe530>
2025-08-07 09:33:42.609 | DEBUG    | llmcompressor.core.lifecycle:event:201 - Updated event with modifier: modifiers=[GPTQModifier(config_groups=None, targets=['Linear'], ignore=['lm_head'], scheme='W8A8', kv_cache_scheme=None, index=1, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=True, ended_=False, sequential_update=True, sequential_targets=None, block_size=128, dampening_frac=0.01, actorder=None, offload_hessians=False)] index=1 group='GPTQModifier' applied=False
2025-08-07 09:33:43.805 | DEBUG    | llmcompressor.core.lifecycle:event:191 - Handling event: EventType.SEQUENTIAL_EPOCH_END
2025-08-07 09:33:43.805 | INFO     | llmcompressor.modifiers.quantization.gptq.base:compress_modules:260 - Quantizing model.layers.0.self_attn.q_proj using 256 samples
2025-08-07 09:33:45.964 | METRIC   | llmcompressor.utils.metric_logging:compress:127 - time 2.16s
2025-08-07 09:33:45.964 | METRIC   | llmcompressor.utils.metric_logging:compress:129 - error 0.01
2025-08-07 09:33:45.972 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 0 | usage: 10.56% | total memory: 42 GB
2025-08-07 09:33:45.972 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 1 | usage: 1.27% | total memory: 42 GB
2025-08-07 09:33:45.972 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 2 | usage: 9.25% | total memory: 42 GB
2025-08-07 09:33:45.972 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 3 | usage: 2.49% | total memory: 42 GB
2025-08-07 09:33:45.973 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 4 | usage: 8.55% | total memory: 42 GB
2025-08-07 09:33:45.973 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 5 | usage: 1.26% | total memory: 42 GB
2025-08-07 09:33:45.973 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 6 | usage: 2.30% | total memory: 42 GB
2025-08-07 09:33:45.973 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 7 | usage: 1.26% | total memory: 42 GB
2025-08-07 09:33:45.974 | METRIC   | llmcompressor.utils.metric_logging:compress:145 - Compressed module size: 0.03584 MB
2025-08-07 09:33:45.974 | INFO     | llmcompressor.modifiers.quantization.gptq.base:compress_modules:260 - Quantizing model.layers.0.self_attn.k_proj using 256 samples
2025-08-07 09:33:45.990 | METRIC   | llmcompressor.utils.metric_logging:compress:127 - time 0.02s
2025-08-07 09:33:45.990 | METRIC   | llmcompressor.utils.metric_logging:compress:129 - error 0.01
2025-08-07 09:33:45.998 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 0 | usage: 10.56% | total memory: 42 GB
2025-08-07 09:33:45.999 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 1 | usage: 1.27% | total memory: 42 GB
2025-08-07 09:33:45.999 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 2 | usage: 9.25% | total memory: 42 GB
2025-08-07 09:33:45.999 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 3 | usage: 2.49% | total memory: 42 GB
2025-08-07 09:33:46.000 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 4 | usage: 8.55% | total memory: 42 GB
2025-08-07 09:33:46.000 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 5 | usage: 1.26% | total memory: 42 GB
2025-08-07 09:33:46.001 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 6 | usage: 2.30% | total memory: 42 GB
2025-08-07 09:33:46.001 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 7 | usage: 1.26% | total memory: 42 GB
2025-08-07 09:33:46.002 | METRIC   | llmcompressor.utils.metric_logging:compress:145 - Compressed module size: 0.03584 MB
2025-08-07 09:33:46.003 | INFO     | llmcompressor.modifiers.quantization.gptq.base:compress_modules:260 - Quantizing model.layers.0.self_attn.v_proj using 256 samples
2025-08-07 09:33:46.027 | METRIC   | llmcompressor.utils.metric_logging:compress:127 - time 0.02s
2025-08-07 09:33:46.027 | METRIC   | llmcompressor.utils.metric_logging:compress:129 - error 0.01
2025-08-07 09:33:46.029 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 0 | usage: 10.57% | total memory: 42 GB
2025-08-07 09:33:46.030 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 1 | usage: 1.27% | total memory: 42 GB
2025-08-07 09:33:46.030 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 2 | usage: 9.25% | total memory: 42 GB
2025-08-07 09:33:46.030 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 3 | usage: 2.49% | total memory: 42 GB
2025-08-07 09:33:46.030 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 4 | usage: 8.55% | total memory: 42 GB
2025-08-07 09:33:46.030 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 5 | usage: 1.26% | total memory: 42 GB
2025-08-07 09:33:46.031 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 6 | usage: 2.30% | total memory: 42 GB
2025-08-07 09:33:46.031 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 7 | usage: 1.26% | total memory: 42 GB
2025-08-07 09:33:46.031 | METRIC   | llmcompressor.utils.metric_logging:compress:145 - Compressed module size: 0.03584 MB
2025-08-07 09:33:46.031 | INFO     | llmcompressor.modifiers.quantization.gptq.base:compress_modules:260 - Quantizing model.layers.0.self_attn.o_proj using 256 samples
2025-08-07 09:33:46.492 | METRIC   | llmcompressor.utils.metric_logging:compress:127 - time 0.46s
2025-08-07 09:33:46.492 | METRIC   | llmcompressor.utils.metric_logging:compress:129 - error 0.00
2025-08-07 09:33:46.493 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 0 | usage: 11.54% | total memory: 42 GB
2025-08-07 09:33:46.493 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 1 | usage: 1.27% | total memory: 42 GB
2025-08-07 09:33:46.493 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 2 | usage: 9.25% | total memory: 42 GB
2025-08-07 09:33:46.493 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 3 | usage: 2.49% | total memory: 42 GB
2025-08-07 09:33:46.493 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 4 | usage: 8.55% | total memory: 42 GB
2025-08-07 09:33:46.493 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 5 | usage: 1.26% | total memory: 42 GB
2025-08-07 09:33:46.493 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 6 | usage: 2.30% | total memory: 42 GB
2025-08-07 09:33:46.493 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 7 | usage: 1.26% | total memory: 42 GB
2025-08-07 09:33:46.494 | METRIC   | llmcompressor.utils.metric_logging:compress:145 - Compressed module size: 0.032816 MB
2025-08-07 09:33:46.494 | INFO     | llmcompressor.modifiers.quantization.gptq.base:compress_modules:260 - Quantizing model.layers.0.mlp.gate_proj using 256 samples
2025-08-07 09:33:46.503 | METRIC   | llmcompressor.utils.metric_logging:compress:127 - time 0.01s
2025-08-07 09:33:46.503 | METRIC   | llmcompressor.utils.metric_logging:compress:129 - error 0.00
2025-08-07 09:33:46.504 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 0 | usage: 11.54% | total memory: 42 GB
2025-08-07 09:33:46.504 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 1 | usage: 1.27% | total memory: 42 GB
2025-08-07 09:33:46.504 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 2 | usage: 9.25% | total memory: 42 GB
2025-08-07 09:33:46.504 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 3 | usage: 2.49% | total memory: 42 GB
2025-08-07 09:33:46.504 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 4 | usage: 8.55% | total memory: 42 GB
2025-08-07 09:33:46.504 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 5 | usage: 1.26% | total memory: 42 GB
2025-08-07 09:33:46.504 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 6 | usage: 2.30% | total memory: 42 GB
2025-08-07 09:33:46.505 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 7 | usage: 1.26% | total memory: 42 GB
2025-08-07 09:33:46.505 | METRIC   | llmcompressor.utils.metric_logging:compress:145 - Compressed module size: 0.00112 MB
2025-08-07 09:33:46.505 | INFO     | llmcompressor.modifiers.quantization.gptq.base:compress_modules:260 - Quantizing model.layers.0.mlp.up_proj using 256 samples
2025-08-07 09:33:46.514 | METRIC   | llmcompressor.utils.metric_logging:compress:127 - time 0.01s
2025-08-07 09:33:46.514 | METRIC   | llmcompressor.utils.metric_logging:compress:129 - error 0.00
2025-08-07 09:33:46.514 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 0 | usage: 11.54% | total memory: 42 GB
2025-08-07 09:33:46.514 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 1 | usage: 1.27% | total memory: 42 GB
2025-08-07 09:33:46.515 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 2 | usage: 9.25% | total memory: 42 GB
2025-08-07 09:33:46.515 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 3 | usage: 2.49% | total memory: 42 GB
2025-08-07 09:33:46.515 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 4 | usage: 8.55% | total memory: 42 GB
2025-08-07 09:33:46.515 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 5 | usage: 1.26% | total memory: 42 GB
2025-08-07 09:33:46.515 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 6 | usage: 2.30% | total memory: 42 GB
2025-08-07 09:33:46.515 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 7 | usage: 1.26% | total memory: 42 GB
2025-08-07 09:33:46.515 | METRIC   | llmcompressor.utils.metric_logging:compress:145 - Compressed module size: 0.00112 MB
2025-08-07 09:33:46.516 | INFO     | llmcompressor.modifiers.quantization.gptq.base:compress_modules:260 - Quantizing model.layers.0.mlp.down_proj using 256 samples
2025-08-07 09:33:46.531 | METRIC   | llmcompressor.utils.metric_logging:compress:127 - time 0.01s
2025-08-07 09:33:46.531 | METRIC   | llmcompressor.utils.metric_logging:compress:129 - error 0.00
2025-08-07 09:33:46.531 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 0 | usage: 11.54% | total memory: 42 GB
2025-08-07 09:33:46.531 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 1 | usage: 1.27% | total memory: 42 GB
2025-08-07 09:33:46.531 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 2 | usage: 9.25% | total memory: 42 GB
2025-08-07 09:33:46.532 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 3 | usage: 2.49% | total memory: 42 GB
2025-08-07 09:33:46.532 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 4 | usage: 8.55% | total memory: 42 GB
2025-08-07 09:33:46.532 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 5 | usage: 1.26% | total memory: 42 GB
2025-08-07 09:33:46.532 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 6 | usage: 2.30% | total memory: 42 GB
2025-08-07 09:33:46.532 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 7 | usage: 1.26% | total memory: 42 GB
2025-08-07 09:33:46.532 | METRIC   | llmcompressor.utils.metric_logging:compress:145 - Compressed module size: 0.001072 MB
2025-08-07 09:33:46.533 | DEBUG    | llmcompressor.core.lifecycle:event:201 - Updated event with modifier: modifiers=[GPTQModifier(config_groups=None, targets=['Linear'], ignore=['lm_head'], scheme='W8A8', kv_cache_scheme=None, index=1, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=True, ended_=False, sequential_update=True, sequential_targets=None, block_size=128, dampening_frac=0.01, actorder=None, offload_hessians=False)] index=1 group='GPTQModifier' applied=False
2025-08-07 09:33:49.630 | DEBUG    | llmcompressor.core.lifecycle:event:191 - Handling event: EventType.SEQUENTIAL_EPOCH_END
2025-08-07 09:33:49.631 | DEBUG    | llmcompressor.core.lifecycle:event:201 - Updated event with modifier: modifiers=[GPTQModifier(config_groups=None, targets=['Linear'], ignore=['lm_head'], scheme='W8A8', kv_cache_scheme=None, index=1, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=True, ended_=False, sequential_update=True, sequential_targets=None, block_size=128, dampening_frac=0.01, actorder=None, offload_hessians=False)] index=1 group='GPTQModifier' applied=False
2025-08-07 09:33:49.734 | DEBUG    | llmcompressor.core.lifecycle:event:191 - Handling event: EventType.CALIBRATION_EPOCH_END
2025-08-07 09:33:49.736 | DEBUG    | llmcompressor.core.lifecycle:event:201 - Updated event with modifier: modifiers=[GPTQModifier(config_groups=None, targets=['Linear'], ignore=['lm_head'], scheme='W8A8', kv_cache_scheme=None, index=1, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=True, ended_=True, sequential_update=True, sequential_targets=None, block_size=128, dampening_frac=0.01, actorder=None, offload_hessians=False)] index=1 group='GPTQModifier' applied=False
2025-08-07 09:33:49.737 | DEBUG    | llmcompressor.core.lifecycle:finalize:131 - Finalizing compression lifecycle
2025-08-07 09:33:49.737 | DEBUG    | llmcompressor.core.lifecycle:finalize:135 - Finalized modifier: modifiers=[SmoothQuantModifier(index=0, group='default', start=None, end=None, update=None, initialized_=True, finalized_=True, started_=True, ended_=True, smoothing_strength=0.8, mappings=[LayerMap(balance_layers=['re:.*q_proj', 're:.*k_proj', 're:.*v_proj'], smooth_layers='re:.*input_layernorm'), LayerMap(balance_layers=['re:.*gate_proj', 're:.*up_proj'], smooth_layers='re:.*post_attention_layernorm')], ignore=[], num_calibration_steps=None, calibration_function=None), GPTQModifier(config_groups=None, targets=['Linear'], ignore=['lm_head'], scheme='W8A8', kv_cache_scheme=None, index=1, group='default', start=None, end=None, update=None, initialized_=True, finalized_=True, started_=True, ended_=True, sequential_update=True, sequential_targets=None, block_size=128, dampening_frac=0.01, actorder=None, offload_hessians=False)] index=0 group='default' applied=True
2025-08-07 09:33:49.738 | INFO     | llmcompressor.core.lifecycle:finalize:141 - Compression lifecycle finalized for 1 modifiers
2025-08-07 09:33:49.743 | WARNING  | llmcompressor.entrypoints.utils:post_process:107 - Optimized model is not saved. To save, please provide`output_dir` as input arg.Ex. `oneshot(..., output_dir=...)`
