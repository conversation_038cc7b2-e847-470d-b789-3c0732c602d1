# 量化参数配置速查表

## 🚀 快速开始模板

### 基础量化代码模板
```python
#!/usr/bin/env python3
from transformers import AutoTokenizer, AutoModelForCausalLM
from datasets import load_dataset
from llmcompressor import oneshot
from llmcompressor.modifiers.quantization import GPTQModifier
from llmcompressor.modifiers.smoothquant import SmoothQuantModifier

# 1. 模型加载
model = AutoModelForCausalLM.from_pretrained(
    "your_model_path",
    device_map="auto",
    torch_dtype="auto"
)
tokenizer = AutoTokenizer.from_pretrained("your_model_path")

# 2. 处理pad_token
if tokenizer.pad_token is None:
    tokenizer.add_special_tokens({'pad_token': '[PAD]'})
    model.resize_token_embeddings(len(tokenizer))

# 3. 数据准备
ds = load_dataset("your_dataset", split="train")
ds = ds.shuffle(seed=42).select(range(512))  # 选择校准样本

def preprocess(example):
    return {"text": tokenizer.apply_chat_template(example["messages"], tokenize=False)}

def tokenize(sample):
    return tokenizer(sample["text"], padding=True, max_length=512, truncation=True)

ds = ds.map(preprocess).map(tokenize, remove_columns=ds.column_names)

# 4. 量化配置
recipe = [
    SmoothQuantModifier(smoothing_strength=0.8),
    GPTQModifier(targets="Linear", scheme="W8A8", ignore=["lm_head"])
]

# 5. 执行量化
oneshot(
    model=model,
    dataset=ds,
    recipe=recipe,
    max_seq_length=512,
    num_calibration_samples=512,
    output_dir="quantized_model"
)
```

## 📊 参数配置对照表

### SmoothQuantModifier参数
| 参数名 | 类型 | 默认值 | 推荐值 | 说明 |
|--------|------|--------|--------|------|
| `smoothing_strength` | float | 0.5 | 0.8 | 平滑强度，范围[0.0-1.0] |
| `mappings` | dict | None | None | 层映射关系，None为自动推断 |
| `num_calibration_steps` | int | None | None | 校准步数，None使用全部数据 |
| `targets` | list | None | None | 目标层，None为自动识别 |

### GPTQModifier参数
| 参数名 | 类型 | 默认值 | 推荐值 | 说明 |
|--------|------|--------|--------|------|
| `targets` | str/list | "Linear" | "Linear" | 目标层类型 |
| `scheme` | str | "W8A16" | "W8A8" | 量化方案 |
| `ignore` | list | [] | ["lm_head"] | 忽略的层 |
| `dampening_frac` | float | 0.01 | 0.01 | 阻尼系数 |
| `block_size` | int | 128 | 128 | 块大小 |
| `sequential_update` | bool | False | False | 是否顺序更新 |

### oneshot函数参数
| 参数名 | 类型 | 必需 | 推荐值 | 说明 |
|--------|------|------|--------|------|
| `model` | Model | ✅ | - | 待量化模型 |
| `dataset` | Dataset | ✅ | - | 校准数据集 |
| `recipe` | list | ✅ | - | 量化配置列表 |
| `max_seq_length` | int | ✅ | 512 | 最大序列长度 |
| `num_calibration_samples` | int | ✅ | 512 | 校准样本数 |
| `output_dir` | str | ❌ | "quantized_model" | 输出目录 |
| `save_compressed` | bool | ❌ | True | 保存压缩格式 |

## 🎯 场景化配置方案

### 1. 开发调试配置
```python
# 快速验证，最小资源消耗
config = {
    "num_calibration_samples": 32,
    "max_sequence_length": 256,
    "smoothing_strength": 0.8,
    "scheme": "W8A8",
    "block_size": 64
}
```

### 2. 标准生产配置
```python
# 平衡性能和精度
config = {
    "num_calibration_samples": 512,
    "max_sequence_length": 512,
    "smoothing_strength": 0.8,
    "scheme": "W8A8",
    "block_size": 128
}
```

### 3. 高精度配置
```python
# 追求最佳精度
config = {
    "num_calibration_samples": 1024,
    "max_sequence_length": 1024,
    "smoothing_strength": 0.5,
    "scheme": "W8A16",
    "block_size": 256
}
```

### 4. 极致压缩配置
```python
# 最大压缩比
config = {
    "num_calibration_samples": 1024,
    "max_sequence_length": 512,
    "smoothing_strength": 1.0,
    "scheme": "W4A16",
    "block_size": 128
}
```

## 🔧 量化方案对比

### 量化精度方案
| 方案 | 权重精度 | 激活精度 | 压缩比 | 精度保持 | 推理速度 |
|------|----------|----------|--------|----------|----------|
| W8A8 | 8位 | 8位 | 4x | 中等 | 最快 |
| W8A16 | 8位 | 16位 | 2x | 较好 | 快 |
| W4A16 | 4位 | 16位 | 4x | 一般 | 中等 |
| W4A8 | 4位 | 8位 | 8x | 较差 | 最快 |

### 平滑强度选择
| 强度值 | 适用场景 | 特点 |
|--------|----------|------|
| 0.3-0.5 | 高精度要求 | 保守平滑，精度损失小 |
| 0.6-0.8 | 标准应用 | 平衡平滑，推荐设置 |
| 0.9-1.0 | 极致压缩 | 激进平滑，压缩比高 |

## 📈 性能调优指南

### 校准样本数量选择
```python
sample_guidelines = {
    "小模型(<1B)": 128,
    "中等模型(1B-7B)": 512,
    "大模型(7B-70B)": 1024,
    "超大模型(>70B)": 2048
}
```

### 序列长度配置
```python
length_guidelines = {
    "对话任务": 256,
    "通用任务": 512,
    "长文本": 1024,
    "文档处理": 2048
}
```

### 内存优化配置
```python
# 内存受限环境
memory_optimized = {
    "device_map": "auto",
    "low_cpu_mem_usage": True,
    "torch_dtype": "auto",
    "num_calibration_samples": 128,
    "max_sequence_length": 256
}
```

## 🚨 常见错误及解决方案

### 1. FX Tracing错误
```bash
# 错误信息
RuntimeError: symbolically traced variables cannot be used as inputs to control flow

# 解决方案
pip install torch==2.5.1+cu124 torchvision==0.20.1+cu124
```

### 2. 内存溢出
```python
# 减少资源消耗
config = {
    "num_calibration_samples": 32,  # 减少样本
    "max_sequence_length": 256,     # 减少长度
    "block_size": 64               # 减少块大小
}
```

### 3. 量化精度下降
```python
# 提高精度配置
config = {
    "smoothing_strength": 0.5,     # 降低平滑强度
    "scheme": "W8A16",             # 使用更高精度
    "num_calibration_samples": 1024 # 增加校准样本
}
```

### 4. 量化速度慢
```python
# 加速配置
config = {
    "num_calibration_samples": 128, # 减少样本
    "block_size": 64,              # 减少块大小
    "sequential_update": False      # 并行更新
}
```

## 📋 检查清单

### 量化前检查
- [ ] PyTorch版本 >= 2.5.1
- [ ] GPU内存充足
- [ ] 模型加载成功
- [ ] pad_token已处理
- [ ] 数据集格式正确

### 量化中监控
- [ ] SmoothQuant平滑完成
- [ ] GPTQ量化误差 < 0.01
- [ ] 内存使用正常
- [ ] 无错误日志

### 量化后验证
- [ ] 模型文件保存完整
- [ ] recipe.yaml配置正确
- [ ] 量化参数生成
- [ ] 推理测试通过

## 🎓 学习建议

1. **从小模型开始**: 使用参数量较小的模型练习
2. **逐步调优**: 先用默认配置，再根据需求调整
3. **监控日志**: 关注量化过程中的误差和时间
4. **对比测试**: 量化前后进行精度和速度对比
5. **文档记录**: 记录不同配置的效果和适用场景

这个速查表提供了量化过程中所有关键参数的配置指南，帮助你快速上手并优化量化效果。
