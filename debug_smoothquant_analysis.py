#!/usr/bin/env python3
"""
详细分析single_llama模型通过llmcompressor使用SmoothQuant量化的原理和流程
包含完整的数据流分析：载入 -> 配置 -> 量化 -> 保存
"""

import torch
import numpy as np
from transformers import AutoTokenizer, AutoModelForCausalLM
from datasets import load_dataset
from llmcompressor.transformers import oneshot
from llmcompressor.modifiers.quantization import GPTQModifier
from llmcompressor.modifiers.smoothquant import SmoothQuantModifier
import json
import os
from typing import Dict, List, Any

class SmoothQuantAnalyzer:
    def __init__(self, model_id: str):
        self.model_id = model_id
        self.analysis_results = {}
        
    def step1_model_loading_analysis(self):
        """步骤1: 模型加载分析"""
        print("=" * 80)
        print("步骤1: 模型加载与架构分析")
        print("=" * 80)
        
        # 加载模型和tokenizer
        print(f"🔍 加载模型: {self.model_id}")
        model = AutoModelForCausalLM.from_pretrained(
            self.model_id, 
            device_map="auto", 
            torch_dtype="auto",
        )
        tokenizer = AutoTokenizer.from_pretrained(self.model_id)
        
        # 分析模型架构
        print("\n📊 模型架构分析:")
        print(f"模型类型: {type(model).__name__}")
        print(f"模型配置: {model.config}")
        
        # 统计模型参数
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"\n📈 参数统计:")
        print(f"总参数数量: {total_params:,}")
        print(f"可训练参数: {trainable_params:,}")
        print(f"模型大小: {total_params * 4 / 1024**3:.2f} GB (FP32)")
        
        # 分析模型层结构
        print(f"\n🏗️ 模型层结构:")
        layer_count = {}
        for name, module in model.named_modules():
            module_type = type(module).__name__
            layer_count[module_type] = layer_count.get(module_type, 0) + 1
            
            # 详细分析Linear层（SmoothQuant的目标）
            if isinstance(module, torch.nn.Linear):
                print(f"  Linear层: {name} -> 输入维度: {module.in_features}, 输出维度: {module.out_features}")
        
        print(f"\n📋 层类型统计:")
        for layer_type, count in sorted(layer_count.items()):
            print(f"  {layer_type}: {count}个")
        
        # 检查tokenizer
        if tokenizer.pad_token is None:
            print(f"\n🔧 添加pad_token")
            tokenizer.add_special_tokens({'pad_token': '[PAD]'})
            model.resize_token_embeddings(len(tokenizer))
            print(f"词汇表大小调整为: {len(tokenizer)}")
        
        self.analysis_results['model_info'] = {
            'model_type': type(model).__name__,
            'total_params': total_params,
            'trainable_params': trainable_params,
            'layer_count': layer_count,
            'vocab_size': len(tokenizer)
        }
        
        return model, tokenizer
    
    def step2_dataset_analysis(self, tokenizer, num_samples=32, max_length=512):
        """步骤2: 数据集加载与预处理分析"""
        print("\n" + "=" * 80)
        print("步骤2: 数据集加载与预处理分析")
        print("=" * 80)
        
        dataset_path = "/workspace/dataset/datasets--HuggingFaceH4--ultrachat_200k/snapshots/8049631c405ae6576f93f445c6b8166f76f5505a/"
        
        print(f"🔍 加载数据集: {dataset_path}")
        ds = load_dataset(dataset_path, split="train_sft")
        
        print(f"📊 原始数据集信息:")
        print(f"  总样本数: {len(ds)}")
        print(f"  列名: {ds.column_names}")
        print(f"  特征: {ds.features}")
        
        # 查看原始数据样本
        print(f"\n📝 原始数据样本:")
        sample = ds[0]
        print(f"  消息数量: {len(sample['messages'])}")
        for i, msg in enumerate(sample['messages'][:2]):  # 只显示前2条消息
            print(f"    消息{i+1}: {msg}")
        
        # 选择校准样本
        ds = ds.shuffle(seed=42).select(range(num_samples))
        print(f"\n🎯 选择校准样本: {num_samples}个")
        
        # 预处理：应用chat template
        def preprocess(example):
            text = tokenizer.apply_chat_template(example["messages"], tokenize=False)
            return {"text": text}
        
        ds = ds.map(preprocess)
        
        # 分析预处理后的文本
        print(f"\n📝 预处理后的文本样本:")
        processed_sample = ds[0]['text']
        print(f"  文本长度: {len(processed_sample)} 字符")
        print(f"  文本预览: {processed_sample[:200]}...")
        
        # Tokenization
        def tokenize(sample):
            return tokenizer(
                sample["text"], 
                padding=True, 
                max_length=max_length, 
                truncation=True, 
                add_special_tokens=False
            )
        
        ds = ds.map(tokenize, remove_columns=ds.column_names)
        
        # 分析tokenization结果
        print(f"\n🔤 Tokenization分析:")
        token_sample = ds[0]
        print(f"  input_ids长度: {len(token_sample['input_ids'])}")
        print(f"  attention_mask长度: {len(token_sample['attention_mask'])}")
        print(f"  input_ids样本: {token_sample['input_ids'][:20]}...")
        
        # 统计token长度分布
        token_lengths = [len(sample['input_ids']) for sample in ds]
        print(f"\n📊 Token长度统计:")
        print(f"  平均长度: {np.mean(token_lengths):.1f}")
        print(f"  最小长度: {min(token_lengths)}")
        print(f"  最大长度: {max(token_lengths)}")
        print(f"  中位数: {np.median(token_lengths):.1f}")
        
        self.analysis_results['dataset_info'] = {
            'num_samples': num_samples,
            'max_length': max_length,
            'avg_token_length': float(np.mean(token_lengths)),
            'min_token_length': min(token_lengths),
            'max_token_length': max(token_lengths)
        }
        
        return ds
    
    def step3_smoothquant_config_analysis(self):
        """步骤3: SmoothQuant配置分析"""
        print("\n" + "=" * 80)
        print("步骤3: SmoothQuant配置分析")
        print("=" * 80)
        
        # 创建SmoothQuant modifier
        smoothquant_modifier = SmoothQuantModifier(smoothing_strength=0.8)
        
        print(f"🔧 SmoothQuant配置:")
        print(f"  平滑强度 (smoothing_strength): {smoothquant_modifier.smoothing_strength}")
        print(f"  映射 (mappings): {smoothquant_modifier.mappings}")
        print(f"  校准步数 (num_calibration_steps): {smoothquant_modifier.num_calibration_steps}")
        
        # 创建GPTQ modifier
        gptq_modifier = GPTQModifier(targets="Linear", scheme="W8A8", ignore=["lm_head"])
        
        print(f"\n🔧 GPTQ配置:")
        print(f"  目标层 (targets): {gptq_modifier.targets}")
        print(f"  量化方案 (scheme): {gptq_modifier.scheme}")
        print(f"  忽略层 (ignore): {gptq_modifier.ignore}")
        
        recipe = [smoothquant_modifier, gptq_modifier]
        
        print(f"\n📋 量化Recipe:")
        for i, modifier in enumerate(recipe):
            print(f"  {i+1}. {type(modifier).__name__}")
        
        self.analysis_results['config_info'] = {
            'smoothquant_strength': smoothquant_modifier.smoothing_strength,
            'gptq_scheme': gptq_modifier.scheme,
            'gptq_targets': gptq_modifier.targets,
            'gptq_ignore': gptq_modifier.ignore
        }
        
        return recipe
    
    def step4_quantization_process_analysis(self, model, dataset, recipe, max_seq_length, num_calibration_samples):
        """步骤4: 量化过程详细分析"""
        print("\n" + "=" * 80)
        print("步骤4: 量化过程详细分析")
        print("=" * 80)
        
        print(f"🚀 开始量化过程...")
        print(f"  最大序列长度: {max_seq_length}")
        print(f"  校准样本数: {num_calibration_samples}")
        
        # 在量化前记录模型状态
        print(f"\n📊 量化前模型状态:")
        self._analyze_model_weights(model, "量化前")
        
        # 执行量化
        oneshot(
            model=model,
            dataset=dataset,
            recipe=recipe,
            max_seq_length=max_seq_length,
            num_calibration_samples=num_calibration_samples,
        )
        
        # 在量化后记录模型状态
        print(f"\n📊 量化后模型状态:")
        self._analyze_model_weights(model, "量化后")
        
        return model
    
    def _analyze_model_weights(self, model, stage):
        """分析模型权重"""
        print(f"\n🔍 {stage}权重分析:")
        
        weight_stats = {}
        for name, param in model.named_parameters():
            if 'weight' in name and param.numel() > 0:
                weight_data = param.data.cpu().numpy().flatten()
                stats = {
                    'shape': list(param.shape),
                    'dtype': str(param.dtype),
                    'mean': float(np.mean(weight_data)),
                    'std': float(np.std(weight_data)),
                    'min': float(np.min(weight_data)),
                    'max': float(np.max(weight_data)),
                    'unique_values': len(np.unique(weight_data))
                }
                weight_stats[name] = stats
                
                # 只打印前几个层的详细信息
                if len(weight_stats) <= 5:
                    print(f"  {name}:")
                    print(f"    形状: {stats['shape']}")
                    print(f"    数据类型: {stats['dtype']}")
                    print(f"    均值: {stats['mean']:.6f}")
                    print(f"    标准差: {stats['std']:.6f}")
                    print(f"    范围: [{stats['min']:.6f}, {stats['max']:.6f}]")
                    print(f"    唯一值数量: {stats['unique_values']}")
        
        self.analysis_results[f'{stage.lower()}_weights'] = weight_stats
    
    def step5_save_analysis(self, model, tokenizer):
        """步骤5: 模型保存分析"""
        print("\n" + "=" * 80)
        print("步骤5: 模型保存分析")
        print("=" * 80)
        
        save_dir = f"{self.model_id.split('/')[-1]}-W8A8-SmoothQuant-Debug"
        
        print(f"💾 保存量化模型到: {save_dir}")
        
        # 保存模型
        model.save_pretrained(save_dir, save_compressed=True)
        tokenizer.save_pretrained(save_dir)
        
        # 分析保存的文件
        print(f"\n📁 保存的文件:")
        for root, dirs, files in os.walk(save_dir):
            for file in files:
                file_path = os.path.join(root, file)
                file_size = os.path.getsize(file_path)
                print(f"  {file}: {file_size / 1024**2:.2f} MB")
        
        # 检查recipe.yaml
        recipe_path = os.path.join(save_dir, "recipe.yaml")
        if os.path.exists(recipe_path):
            print(f"\n📋 Recipe文件内容:")
            with open(recipe_path, 'r') as f:
                recipe_content = f.read()
                print(recipe_content)
        
        self.analysis_results['save_info'] = {
            'save_dir': save_dir,
            'files': os.listdir(save_dir)
        }
        
        return save_dir
    
    def generate_analysis_report(self):
        """生成完整的分析报告"""
        print("\n" + "=" * 80)
        print("生成完整分析报告")
        print("=" * 80)
        
        report_path = "smoothquant_analysis_report.json"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, indent=2, ensure_ascii=False)
        
        print(f"📄 分析报告已保存到: {report_path}")
        
        return report_path

def main():
    """主函数：执行完整的SmoothQuant分析流程"""
    print("SmoothQuant量化原理与流程详细分析")
    print("=" * 80)
    
    # 初始化分析器
    model_id = "/home/<USER>/single_llama"
    analyzer = SmoothQuantAnalyzer(model_id)
    
    # 步骤1: 模型加载分析
    model, tokenizer = analyzer.step1_model_loading_analysis()
    
    # 步骤2: 数据集分析
    dataset = analyzer.step2_dataset_analysis(tokenizer, num_samples=32, max_length=512)
    
    # 步骤3: 配置分析
    recipe = analyzer.step3_smoothquant_config_analysis()
    
    # 步骤4: 量化过程分析
    quantized_model = analyzer.step4_quantization_process_analysis(
        model, dataset, recipe, max_seq_length=512, num_calibration_samples=32
    )
    
    # 步骤5: 保存分析
    save_dir = analyzer.step5_save_analysis(quantized_model, tokenizer)
    
    # 生成报告
    report_path = analyzer.generate_analysis_report()
    
    print(f"\n🎉 分析完成!")
    print(f"📊 量化模型保存在: {save_dir}")
    print(f"📄 详细报告保存在: {report_path}")

if __name__ == "__main__":
    main()
