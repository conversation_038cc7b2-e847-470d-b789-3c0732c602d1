{"model_info": {"model_type": "LlamaForCausalLM", "total_params": 68144, "trainable_params": 68144, "layer_count": {"LlamaForCausalLM": 1, "LlamaModel": 1, "Embedding": 1, "ModuleList": 1, "LlamaDecoderLayer": 1, "LlamaAttention": 1, "Linear": 8, "LlamaMLP": 1, "SiLU": 1, "LlamaRMSNorm": 3, "LlamaRotaryEmbedding": 1}, "vocab_size": 32001}, "dataset_info": {"num_samples": 32, "max_length": 512, "avg_token_length": 512.0, "min_token_length": 512, "max_token_length": 512}, "config_info": {"smoothquant_strength": 0.8, "gptq_scheme": "W8A8", "gptq_targets": ["Linear"], "gptq_ignore": ["lm_head"]}, "量化前_weights": {"model.embed_tokens.weight": {"shape": [32001, 16], "dtype": "torch.float16", "mean": -3.510713577270508e-05, "std": 0.002315521240234375, "min": -0.0718994140625, "max": 0.06591796875, "unique_values": 718}, "model.layers.0.self_attn.q_proj.weight": {"shape": [1024, 16], "dtype": "torch.float16", "mean": 0.0001976490020751953, "std": 0.0199737548828125, "min": -0.073974609375, "max": 0.0767822265625, "unique_values": 8101}, "model.layers.0.self_attn.k_proj.weight": {"shape": [1024, 16], "dtype": "torch.float16", "mean": 2.8014183044433594e-05, "std": 0.019927978515625, "min": -0.0743408203125, "max": 0.074951171875, "unique_values": 8156}, "model.layers.0.self_attn.v_proj.weight": {"shape": [1024, 16], "dtype": "torch.float16", "mean": -0.00017845630645751953, "std": 0.0200653076171875, "min": -0.08697509765625, "max": 0.08367919921875, "unique_values": 8142}, "model.layers.0.self_attn.o_proj.weight": {"shape": [16, 1024], "dtype": "torch.float16", "mean": -7.790327072143555e-05, "std": 0.02001953125, "min": -0.07513427734375, "max": 0.08050537109375, "unique_values": 8189}, "model.layers.0.mlp.gate_proj.weight": {"shape": [32, 16], "dtype": "torch.float16", "mean": -0.0004253387451171875, "std": 0.0196533203125, "min": -0.06658935546875, "max": 0.05230712890625, "unique_values": 502}, "model.layers.0.mlp.up_proj.weight": {"shape": [32, 16], "dtype": "torch.float16", "mean": -0.0006008148193359375, "std": 0.01971435546875, "min": -0.066650390625, "max": 0.0631103515625, "unique_values": 502}, "model.layers.0.mlp.down_proj.weight": {"shape": [16, 32], "dtype": "torch.float16", "mean": -0.0009317398071289062, "std": 0.0200347900390625, "min": -0.05755615234375, "max": 0.058807373046875, "unique_values": 499}, "model.layers.0.input_layernorm.weight": {"shape": [16], "dtype": "torch.float16", "mean": 1.0, "std": 0.0, "min": 1.0, "max": 1.0, "unique_values": 1}, "model.layers.0.post_attention_layernorm.weight": {"shape": [16], "dtype": "torch.float16", "mean": 1.0, "std": 0.0, "min": 1.0, "max": 1.0, "unique_values": 1}, "model.norm.weight": {"shape": [16], "dtype": "torch.float16", "mean": 1.0, "std": 0.0, "min": 1.0, "max": 1.0, "unique_values": 1}, "lm_head.weight": {"shape": [32001, 16], "dtype": "torch.float16", "mean": -0.0008893013000488281, "std": 0.003875732421875, "min": -0.058685302734375, "max": 0.0650634765625, "unique_values": 690}}, "量化后_weights": {"model.embed_tokens.weight": {"shape": [32001, 16], "dtype": "torch.float16", "mean": -3.510713577270508e-05, "std": 0.002315521240234375, "min": -0.0718994140625, "max": 0.06591796875, "unique_values": 718}, "model.layers.0.self_attn.q_proj.weight": {"shape": [1024, 16], "dtype": "torch.float16", "mean": 0.00013017654418945312, "std": 0.048370361328125, "min": -0.275146484375, "max": 0.302001953125, "unique_values": 8587}, "model.layers.0.self_attn.q_proj.weight_scale": {"shape": [1024, 1], "dtype": "torch.float16", "mean": 0.0008945465087890625, "std": 0.000244140625, "min": 0.0002949237823486328, "max": 0.0023784637451171875, "unique_values": 761}, "model.layers.0.self_attn.q_proj.weight_zero_point": {"shape": [1024, 1], "dtype": "torch.int8", "mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0, "unique_values": 1}, "model.layers.0.self_attn.k_proj.weight": {"shape": [1024, 16], "dtype": "torch.float16", "mean": 9.459257125854492e-05, "std": 0.0479736328125, "min": -0.276611328125, "max": 0.324462890625, "unique_values": 8540}, "model.layers.0.self_attn.k_proj.weight_scale": {"shape": [1024, 1], "dtype": "torch.float16", "mean": 0.0008907318115234375, "std": 0.000244140625, "min": 0.0003662109375, "max": 0.0025539398193359375, "unique_values": 775}, "model.layers.0.self_attn.k_proj.weight_zero_point": {"shape": [1024, 1], "dtype": "torch.int8", "mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0, "unique_values": 1}, "model.layers.0.self_attn.v_proj.weight": {"shape": [1024, 16], "dtype": "torch.float16", "mean": -0.0005650520324707031, "std": 0.048004150390625, "min": -0.2607421875, "max": 0.2384033203125, "unique_values": 8495}, "model.layers.0.self_attn.v_proj.weight_scale": {"shape": [1024, 1], "dtype": "torch.float16", "mean": 0.0008854866027832031, "std": 0.000244140625, "min": 0.00035119056701660156, "max": 0.00205230712890625, "unique_values": 757}, "model.layers.0.self_attn.v_proj.weight_zero_point": {"shape": [1024, 1], "dtype": "torch.int8", "mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0, "unique_values": 1}, "model.layers.0.self_attn.o_proj.weight": {"shape": [16, 1024], "dtype": "torch.float16", "mean": -7.766485214233398e-05, "std": 0.02001953125, "min": -0.075439453125, "max": 0.0802001953125, "unique_values": 2092}, "model.layers.0.self_attn.o_proj.weight_scale": {"shape": [16, 1], "dtype": "torch.float16", "mean": 0.0005459785461425781, "std": 0.0, "min": 0.00047516822814941406, "max": 0.0006313323974609375, "unique_values": 14}, "model.layers.0.self_attn.o_proj.weight_zero_point": {"shape": [16, 1], "dtype": "torch.int8", "mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0, "unique_values": 1}, "model.layers.0.mlp.gate_proj.weight": {"shape": [32, 16], "dtype": "torch.float16", "mean": -0.00027871131896972656, "std": 0.04473876953125, "min": -0.1292724609375, "max": 0.1396484375, "unique_values": 478}, "model.layers.0.mlp.gate_proj.weight_scale": {"shape": [32, 1], "dtype": "torch.float16", "mean": 0.0007910728454589844, "std": 0.0, "min": 0.0004782676696777344, "max": 0.0011005401611328125, "unique_values": 31}, "model.layers.0.mlp.gate_proj.weight_zero_point": {"shape": [32, 1], "dtype": "torch.int8", "mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0, "unique_values": 1}, "model.layers.0.mlp.up_proj.weight": {"shape": [32, 16], "dtype": "torch.float16", "mean": -0.0020542144775390625, "std": 0.047698974609375, "min": -0.1822509765625, "max": 0.134521484375, "unique_values": 477}, "model.layers.0.mlp.up_proj.weight_scale": {"shape": [32, 1], "dtype": "torch.float16", "mean": 0.0009164810180664062, "std": 0.000244140625, "min": 0.0004353523254394531, "max": 0.001434326171875, "unique_values": 32}, "model.layers.0.mlp.up_proj.weight_zero_point": {"shape": [32, 1], "dtype": "torch.int8", "mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0, "unique_values": 1}, "model.layers.0.mlp.down_proj.weight": {"shape": [16, 32], "dtype": "torch.float16", "mean": -0.0009303092956542969, "std": 0.02001953125, "min": -0.057342529296875, "max": 0.058563232421875, "unique_values": 460}, "model.layers.0.mlp.down_proj.weight_scale": {"shape": [16, 1], "dtype": "torch.float16", "mean": 0.0003657341003417969, "std": 0.0, "min": 0.0002295970916748047, "max": 0.00046133995056152344, "unique_values": 15}, "model.layers.0.mlp.down_proj.weight_zero_point": {"shape": [16, 1], "dtype": "torch.int8", "mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0, "unique_values": 1}, "model.layers.0.input_layernorm.weight": {"shape": [16], "dtype": "torch.float16", "mean": 0.62353515625, "std": 0.435546875, "min": 0.230224609375, "max": 1.74609375, "unique_values": 16}, "model.layers.0.post_attention_layernorm.weight": {"shape": [16], "dtype": "torch.float16", "mean": 0.4921875, "std": 0.1724853515625, "min": 0.253173828125, "max": 0.94970703125, "unique_values": 16}, "model.norm.weight": {"shape": [16], "dtype": "torch.float16", "mean": 1.0, "std": 0.0, "min": 1.0, "max": 1.0, "unique_values": 1}, "lm_head.weight": {"shape": [32001, 16], "dtype": "torch.float16", "mean": -0.0008893013000488281, "std": 0.003875732421875, "min": -0.058685302734375, "max": 0.0650634765625, "unique_values": 690}}, "save_info": {"save_dir": "single_llama-W8A8-SmoothQuant-Debug", "files": ["config.json", "generation_config.json", "model.safetensors", "recipe.yaml", "chat_template.jinja", "tokenizer_config.json", "special_tokens_map.json", "tokenizer.json"]}}