#!/usr/bin/env python3
"""
Debug脚本：测试不同transformers版本是否能解决FX tracing错误
基于GitHub issue: https://github.com/vllm-project/llm-compressor/issues/1603
"""

import subprocess
import sys
import os
import importlib

def check_current_versions():
    """检查当前版本"""
    print("=== 当前环境版本信息 ===")
    
    try:
        import torch
        print(f"PyTorch版本: {torch.__version__}")
    except ImportError:
        print("PyTorch未安装")
    
    try:
        import transformers
        print(f"Transformers版本: {transformers.__version__}")
    except ImportError:
        print("Transformers未安装")
    
    try:
        import llmcompressor
        print(f"LLMCompressor版本: {llmcompressor.__version__}")
    except ImportError:
        print("LLMCompressor未安装")
    
    print()

def install_transformers_version(version):
    """使用uv安装指定版本的transformers"""
    print(f"=== 安装transformers {version} ===")
    
    try:
        # 使用uv安装指定版本
        cmd = ["uv", "pip", "install", f"transformers=={version}", "--force-reinstall"]
        print(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print("安装成功!")
        print(result.stdout)
        
        # 重新导入transformers以获取新版本
        if 'transformers' in sys.modules:
            importlib.reload(sys.modules['transformers'])
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False
    except FileNotFoundError:
        print("错误: 未找到uv命令，请先安装uv")
        print("安装命令: curl -LsSf https://astral.sh/uv/install.sh | sh")
        return False

def test_original_code():
    """测试原始代码是否能正常运行"""
    print("=== 测试原始SmoothQuant代码 ===")
    
    try:
        import torch
        from transformers import AutoTokenizer, AutoModelForCausalLM
        from datasets import load_dataset
        from llmcompressor.transformers import oneshot
        from llmcompressor.modifiers.quantization import GPTQModifier
        from llmcompressor.modifiers.smoothquant import SmoothQuantModifier
        
        print("✅ 所有模块导入成功")
        
        # 模型路径和设备配置
        MODEL_ID = "/home/<USER>/single_llama"
        
        print("加载模型和tokenizer...")
        model = AutoModelForCausalLM.from_pretrained(
            MODEL_ID, device_map="auto", torch_dtype="auto",
        )
        tokenizer = AutoTokenizer.from_pretrained(MODEL_ID)
        
        # 检查并添加填充标记
        if tokenizer.pad_token is None:
            tokenizer.add_special_tokens({'pad_token': '[PAD]'})
            model.resize_token_embeddings(len(tokenizer))
        
        print("✅ 模型加载成功")
        
        # 数据集加载和预处理（使用较小的样本数进行快速测试）
        NUM_CALIBRATION_SAMPLES = 32  # 减少样本数以加快测试
        MAX_SEQUENCE_LENGTH = 512     # 减少序列长度
        
        print("加载数据集...")
        ds = load_dataset("/workspace/dataset/datasets--HuggingFaceH4--ultrachat_200k/snapshots/8049631c405ae6576f93f445c6b8166f76f5505a/", split="train_sft")
        ds = ds.shuffle(seed=42).select(range(NUM_CALIBRATION_SAMPLES))
        
        def preprocess(example):
            return {"text": tokenizer.apply_chat_template(example["messages"], tokenize=False)}
        
        ds = ds.map(preprocess)
        
        def tokenize(sample):
            return tokenizer(sample["text"], padding=True, max_length=MAX_SEQUENCE_LENGTH, truncation=True, add_special_tokens=False)
        
        ds = ds.map(tokenize, remove_columns=ds.column_names)
        
        print("✅ 数据集准备完成")
        
        # 配置量化算法 - 使用原始配置（不添加任何修复）
        recipe = [
            SmoothQuantModifier(smoothing_strength=0.8),
            GPTQModifier(targets="Linear", scheme="W8A8", ignore=["lm_head"]),
        ]
        
        print("开始量化测试...")
        
        # 应用量化
        oneshot(
            model=model,
            dataset=ds,
            recipe=recipe,
            max_seq_length=MAX_SEQUENCE_LENGTH,
            num_calibration_samples=NUM_CALIBRATION_SAMPLES,
        )
        
        print("✅ 量化测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 量化测试失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        
        # 检查是否是FX tracing错误
        if "symbolically traced variables cannot be used as inputs to control flow" in str(e):
            print("🔍 确认这是FX tracing错误")
        
        return False

def main():
    """主函数：测试不同transformers版本"""
    print("Transformers版本兼容性测试")
    print("=" * 60)
    
    # 检查当前版本
    check_current_versions()
    
    # 要测试的transformers版本列表
    # 基于GitHub issue和兼容性考虑
    test_versions = [
        "4.45.0",  # 较早的稳定版本
        "4.46.0",  # 
        "4.47.0",  # 
        "4.48.0",  # 
        "4.49.0",  # 
        "4.50.0",  # 
        "4.51.0",  # 
        "4.52.0",  # 
        "4.53.0",  # issue中提到的版本
    ]
    
    successful_versions = []
    failed_versions = []
    
    for version in test_versions:
        print(f"\n{'='*60}")
        print(f"测试 transformers {version}")
        print(f"{'='*60}")
        
        # 安装指定版本
        if install_transformers_version(version):
            # 验证安装
            try:
                import transformers
                actual_version = transformers.__version__
                print(f"实际安装版本: {actual_version}")
                
                # 测试原始代码
                if test_original_code():
                    print(f"✅ transformers {version} 测试成功!")
                    successful_versions.append(version)
                else:
                    print(f"❌ transformers {version} 测试失败!")
                    failed_versions.append(version)
                    
            except Exception as e:
                print(f"❌ 版本验证失败: {e}")
                failed_versions.append(version)
        else:
            print(f"❌ transformers {version} 安装失败!")
            failed_versions.append(version)
    
    # 总结结果
    print(f"\n{'='*60}")
    print("测试结果总结")
    print(f"{'='*60}")
    
    if successful_versions:
        print(f"✅ 成功的版本 ({len(successful_versions)}):")
        for version in successful_versions:
            print(f"  - transformers {version}")
    else:
        print("❌ 没有成功的版本")
    
    if failed_versions:
        print(f"\n❌ 失败的版本 ({len(failed_versions)}):")
        for version in failed_versions:
            print(f"  - transformers {version}")
    
    # 推荐
    if successful_versions:
        recommended = successful_versions[0]
        print(f"\n🎯 推荐使用版本: transformers {recommended}")
        print(f"安装命令: uv pip install transformers=={recommended}")
    else:
        print(f"\n💡 建议:")
        print("1. 所有测试版本都失败，可能需要:")
        print("   - 使用更早的transformers版本 (如 4.40.x)")
        print("   - 或者继续使用我们之前的环境变量修复方案")
        print("2. 检查是否有其他依赖冲突")

if __name__ == "__main__":
    main()
