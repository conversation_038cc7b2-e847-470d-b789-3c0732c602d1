#!/usr/bin/env python3
"""
简化的Debug脚本：验证oneshot函数的实际执行和源码位置
"""

import torch
import time
from transformers import AutoTokenizer, AutoModelForCausalLM
from datasets import load_dataset
# 先不导入oneshot函数，在函数内部导入
from llmcompressor.modifiers.quantization import GPTQModifier
from llmcompressor.modifiers.smoothquant import SmoothQuantModifier

def verify_source_locations():
    """验证关键源码位置和函数"""
    print("🔍 验证源码位置和函数")
    print("="*80)

    # 1. 验证oneshot函数
    import llmcompressor.entrypoints as entrypoints_module
    from llmcompressor.entrypoints.oneshot import oneshot
    print(f"✅ entrypoints模块位置: {entrypoints_module.__file__}")
    print(f"✅ oneshot函数: {oneshot}")
    
    # 2. 验证Oneshot类
    oneshot_class = entrypoints_module.Oneshot
    print(f"✅ Oneshot类: {oneshot_class}")
    print(f"✅ Oneshot.__init__: {oneshot_class.__init__}")
    print(f"✅ Oneshot.__call__: {oneshot_class.__call__}")
    print(f"✅ Oneshot.apply_recipe_modifiers: {oneshot_class.apply_recipe_modifiers}")
    
    # 3. 验证CompressionLifecycle
    from llmcompressor.core.lifecycle import CompressionLifecycle
    print(f"✅ CompressionLifecycle: {CompressionLifecycle}")
    print(f"✅ CompressionLifecycle.initialize: {CompressionLifecycle.initialize}")
    print(f"✅ CompressionLifecycle.finalize: {CompressionLifecycle.finalize}")
    
    # 4. 验证SmoothQuantModifier
    print(f"✅ SmoothQuantModifier: {SmoothQuantModifier}")
    print(f"✅ SmoothQuantModifier.on_initialize: {SmoothQuantModifier.on_initialize}")
    print(f"✅ SmoothQuantModifier._apply_smoothing: {SmoothQuantModifier._apply_smoothing}")
    
    # 5. 验证GPTQModifier
    print(f"✅ GPTQModifier: {GPTQModifier}")
    print(f"✅ GPTQModifier.on_initialize: {GPTQModifier.on_initialize}")
    print(f"✅ GPTQModifier.compress_modules: {GPTQModifier.compress_modules}")

def inspect_oneshot_source():
    """检查oneshot函数的源码"""
    print("\n🔍 检查oneshot函数源码")
    print("="*80)
    
    import inspect
    from llmcompressor.entrypoints.oneshot import oneshot, Oneshot
    
    # 获取oneshot函数源码
    try:
        oneshot_source = inspect.getsource(oneshot)
        print("📄 oneshot函数源码片段:")
        lines = oneshot_source.split('\n')[:20]  # 只显示前20行
        for i, line in enumerate(lines, 1):
            print(f"{i:2d}: {line}")
        print("    ... (更多内容)")
    except Exception as e:
        print(f"❌ 无法获取oneshot源码: {e}")
    
    # 获取Oneshot类的关键方法源码
    try:
        print("\n📄 Oneshot.__call__方法源码:")
        call_source = inspect.getsource(Oneshot.__call__)
        lines = call_source.split('\n')
        for i, line in enumerate(lines, 1):
            print(f"{i:2d}: {line}")
    except Exception as e:
        print(f"❌ 无法获取Oneshot.__call__源码: {e}")

def trace_oneshot_execution():
    """追踪oneshot执行过程"""
    print("\n🚀 追踪oneshot执行过程")
    print("="*80)
    
    # 1. 准备数据
    MODEL_ID = "/home/<USER>/single_llama"
    
    print("📥 加载模型和数据...")
    start_time = time.time()
    
    model = AutoModelForCausalLM.from_pretrained(
        MODEL_ID, device_map="auto", torch_dtype="auto"
    )
    tokenizer = AutoTokenizer.from_pretrained(MODEL_ID)
    
    if tokenizer.pad_token is None:
        tokenizer.add_special_tokens({'pad_token': '[PAD]'})
        model.resize_token_embeddings(len(tokenizer))
    
    load_time = time.time() - start_time
    print(f"✅ 模型加载完成 ({load_time:.2f}s)")
    
    # 2. 准备数据集
    print("📊 准备数据集...")
    ds = load_dataset("/workspace/dataset/datasets--HuggingFaceH4--ultrachat_200k/snapshots/8049631c405ae6576f93f445c6b8166f76f5505a/", split="train_sft")
    ds = ds.shuffle(seed=42).select(range(32))
    
    def preprocess(example):
        return {"text": tokenizer.apply_chat_template(example["messages"], tokenize=False)}
    
    def tokenize(sample):
        return tokenizer(sample["text"], padding=True, max_length=512, truncation=True, add_special_tokens=False)
    
    ds = ds.map(preprocess).map(tokenize, remove_columns=ds.column_names)
    print("✅ 数据集准备完成")
    
    # 3. 创建recipe
    print("⚙️ 创建量化recipe...")
    recipe = [
        SmoothQuantModifier(smoothing_strength=0.8),
        GPTQModifier(targets="Linear", scheme="W8A8", ignore=["lm_head"]),
    ]
    print(f"✅ Recipe创建完成: {len(recipe)}个修改器")
    for i, modifier in enumerate(recipe, 1):
        print(f"   {i}. {type(modifier).__name__}")
    
    # 4. 执行oneshot
    print("\n🔄 执行oneshot量化...")
    print("="*40)
    
    quantization_start = time.time()
    
    try:
        # 这里是关键的oneshot调用
        from llmcompressor.entrypoints.oneshot import oneshot
        print("📍 调用oneshot函数...")
        print(f"   函数位置: {oneshot}")
        print(f"   参数: model={type(model).__name__}, dataset={len(ds)}样本, recipe={len(recipe)}修改器")

        quantized_model = oneshot(
            model=model,
            dataset=ds,
            recipe=recipe,
            max_seq_length=512,
            num_calibration_samples=32,
        )
        
        quantization_time = time.time() - quantization_start
        print(f"✅ oneshot执行成功! ({quantization_time:.2f}s)")
        print(f"📊 量化模型类型: {type(quantized_model)}")
        
        return quantized_model
        
    except Exception as e:
        print(f"❌ oneshot执行失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_execution_results(quantized_model):
    """分析执行结果"""
    if quantized_model is None:
        print("\n❌ 无法分析结果：量化失败")
        return
    
    print("\n📊 分析量化结果")
    print("="*80)
    
    # 1. 检查模型参数
    total_params = sum(p.numel() for p in quantized_model.parameters())
    print(f"📈 量化后参数数量: {total_params:,}")
    
    # 2. 检查量化参数
    quantized_params = 0
    for name, param in quantized_model.named_parameters():
        if 'weight_scale' in name or 'weight_zero_point' in name:
            quantized_params += 1
            print(f"🔧 量化参数: {name} - {param.shape} ({param.dtype})")
    
    print(f"📊 新增量化参数: {quantized_params}个")
    
    # 3. 检查模型状态
    print(f"📋 模型设备: {next(quantized_model.parameters()).device}")
    print(f"📋 模型数据类型: {next(quantized_model.parameters()).dtype}")

def main():
    """主函数"""
    print("🔍 简化Debug: oneshot执行流程验证")
    print("基于实际源码的执行路径分析")
    print("="*80)
    
    # 1. 验证源码位置
    verify_source_locations()
    
    # 2. 检查源码
    inspect_oneshot_source()
    
    # 3. 执行追踪
    quantized_model = trace_oneshot_execution()
    
    # 4. 分析结果
    analyze_execution_results(quantized_model)
    
    print("\n🎉 Debug完成!")
    print("="*80)
    print("关键发现:")
    print("1. oneshot函数位于: llmcompressor/entrypoints/oneshot.py")
    print("2. 核心流程: oneshot() -> Oneshot() -> apply_recipe_modifiers()")
    print("3. 修改器执行: SmoothQuantModifier -> GPTQModifier")
    print("4. 生命周期管理: CompressionLifecycle.initialize/finalize")

if __name__ == "__main__":
    main()
