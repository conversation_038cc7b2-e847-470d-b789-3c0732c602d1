{"tutorial_config": {"model": {"path": "/home/<USER>/single_llama", "type": "LlamaForCausalLM", "total_params": 68144, "vocab_size": 32001, "linear_layers": [{"name": "model.layers.0.self_attn.q_proj", "in_features": 16, "out_features": 1024, "bias": false}, {"name": "model.layers.0.self_attn.k_proj", "in_features": 16, "out_features": 1024, "bias": false}, {"name": "model.layers.0.self_attn.v_proj", "in_features": 16, "out_features": 1024, "bias": false}, {"name": "model.layers.0.self_attn.o_proj", "in_features": 1024, "out_features": 16, "bias": false}, {"name": "model.layers.0.mlp.gate_proj", "in_features": 16, "out_features": 32, "bias": false}, {"name": "model.layers.0.mlp.up_proj", "in_features": 16, "out_features": 32, "bias": false}, {"name": "model.layers.0.mlp.down_proj", "in_features": 32, "out_features": 16, "bias": false}, {"name": "lm_head", "in_features": 16, "out_features": 32, "bias": false}]}, "data": {"num_calibration_samples": 32, "max_sequence_length": 512, "dataset_path": "/workspace/dataset/datasets--HuggingFaceH4--ultrachat_200k/snapshots/8049631c405ae6576f93f445c6b8166f76f5505a/", "split": "train_sft", "token_stats": {"avg_length": 512.0, "min_length": 512, "max_length": 512}}, "quantization": {"smoothing_strength": 0.8, "scheme": "W8A8", "recipe_order": ["SmoothQuantModifier", "GPTQModifier"]}}, "performance_metrics": {"load_time": 0.2958385944366455, "quantization_time": 6.958118438720703, "compression_ratio": 0.023715021613369}, "debug_information": {"environment": {"torch_version": "2.5.1+cu124", "cuda_available": true, "gpu_count": 8}}}