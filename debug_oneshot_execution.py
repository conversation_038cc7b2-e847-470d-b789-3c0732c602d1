#!/usr/bin/env python3
"""
Debug脚本：实际验证oneshot函数的执行路径和代码位置
通过在关键位置插入调试信息来追踪代码执行流程
"""

import torch
import inspect
import functools
from transformers import AutoTokenizer, AutoModelForCausalLM
from datasets import load_dataset
from llmcompressor.modifiers.quantization import GPTQModifier
from llmcompressor.modifiers.smoothquant import SmoothQuantModifier

class ExecutionTracker:
    """执行路径追踪器"""
    
    def __init__(self):
        self.call_stack = []
        self.step_counter = 0
    
    def trace_call(self, func_name, file_path, line_number, args_info=""):
        """记录函数调用"""
        self.step_counter += 1
        call_info = {
            'step': self.step_counter,
            'function': func_name,
            'file': file_path,
            'line': line_number,
            'args': args_info
        }
        self.call_stack.append(call_info)
        print(f"[{self.step_counter:02d}] {func_name} @ {file_path}:{line_number}")
        if args_info:
            print(f"     Args: {args_info}")
    
    def print_summary(self):
        """打印执行总结"""
        print("\n" + "="*80)
        print("执行路径总结")
        print("="*80)
        for call in self.call_stack:
            print(f"{call['step']:02d}. {call['function']} @ {call['file']}:{call['line']}")

# 全局追踪器
tracker = ExecutionTracker()

def trace_function(func):
    """装饰器：追踪函数执行"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # 获取调用信息
        frame = inspect.currentframe()
        filename = frame.f_code.co_filename
        line_number = frame.f_lineno
        
        # 简化参数信息
        args_info = f"args={len(args)}, kwargs={list(kwargs.keys())[:3]}"
        
        # 记录调用
        tracker.trace_call(func.__name__, filename, line_number, args_info)
        
        # 执行原函数
        result = func(*args, **kwargs)
        return result
    return wrapper

def patch_oneshot_functions():
    """给关键函数打补丁以追踪执行"""

    # 1. 追踪oneshot入口函数
    from llmcompressor.entrypoints.oneshot import oneshot as original_oneshot_func
    import llmcompressor.entrypoints.oneshot as oneshot_module
    
    @trace_function
    def traced_oneshot(*args, **kwargs):
        return original_oneshot_func(*args, **kwargs)

    oneshot_module.oneshot = traced_oneshot

    # 返回traced函数供后续使用
    global traced_oneshot_func
    traced_oneshot_func = traced_oneshot
    
    # 2. 追踪Oneshot类方法
    original_oneshot_init = oneshot_module.Oneshot.__init__
    original_oneshot_call = oneshot_module.Oneshot.__call__
    original_apply_recipe = oneshot_module.Oneshot.apply_recipe_modifiers
    
    @trace_function
    def traced_oneshot_init(self, *args, **kwargs):
        return original_oneshot_init(self, *args, **kwargs)
    
    @trace_function  
    def traced_oneshot_call(self):
        return original_oneshot_call(self)
    
    @trace_function
    def traced_apply_recipe(self, *args, **kwargs):
        return original_apply_recipe(self, *args, **kwargs)
    
    oneshot_module.Oneshot.__init__ = traced_oneshot_init
    oneshot_module.Oneshot.__call__ = traced_oneshot_call
    oneshot_module.Oneshot.apply_recipe_modifiers = traced_apply_recipe
    
    # 3. 追踪CompressionLifecycle
    import llmcompressor.core.lifecycle as lifecycle_module
    original_initialize = lifecycle_module.CompressionLifecycle.initialize
    original_finalize = lifecycle_module.CompressionLifecycle.finalize
    
    @trace_function
    def traced_initialize(self, *args, **kwargs):
        return original_initialize(self, *args, **kwargs)
    
    @trace_function
    def traced_finalize(self, *args, **kwargs):
        return original_finalize(self, *args, **kwargs)
    
    lifecycle_module.CompressionLifecycle.initialize = traced_initialize
    lifecycle_module.CompressionLifecycle.finalize = traced_finalize
    
    # 4. 追踪SmoothQuantModifier
    import llmcompressor.modifiers.smoothquant.base as smoothquant_module
    original_sq_init = smoothquant_module.SmoothQuantModifier.on_initialize
    original_sq_apply = smoothquant_module.SmoothQuantModifier._apply_smoothing
    
    @trace_function
    def traced_sq_init(self, *args, **kwargs):
        return original_sq_init(self, *args, **kwargs)
    
    @trace_function
    def traced_sq_apply(self, *args, **kwargs):
        return original_sq_apply(self, *args, **kwargs)
    
    smoothquant_module.SmoothQuantModifier.on_initialize = traced_sq_init
    smoothquant_module.SmoothQuantModifier._apply_smoothing = traced_sq_apply
    
    # 5. 追踪GPTQModifier
    import llmcompressor.modifiers.quantization.gptq.base as gptq_module
    original_gptq_init = gptq_module.GPTQModifier.on_initialize
    original_gptq_compress = gptq_module.GPTQModifier.compress_modules
    
    @trace_function
    def traced_gptq_init(self, *args, **kwargs):
        return original_gptq_init(self, *args, **kwargs)
    
    @trace_function
    def traced_gptq_compress(self, *args, **kwargs):
        return original_gptq_compress(self, *args, **kwargs)
    
    gptq_module.GPTQModifier.on_initialize = traced_gptq_init
    gptq_module.GPTQModifier.compress_modules = traced_gptq_compress

def debug_oneshot_execution():
    """执行debug版本的oneshot量化"""
    
    print("🔍 开始Debug oneshot执行流程")
    print("="*80)
    
    # 1. 打补丁
    patch_oneshot_functions()
    
    # 2. 准备数据
    MODEL_ID = "/home/<USER>/single_llama"
    
    print("📥 加载模型和数据...")
    model = AutoModelForCausalLM.from_pretrained(
        MODEL_ID, device_map="auto", torch_dtype="auto"
    )
    tokenizer = AutoTokenizer.from_pretrained(MODEL_ID)
    
    if tokenizer.pad_token is None:
        tokenizer.add_special_tokens({'pad_token': '[PAD]'})
        model.resize_token_embeddings(len(tokenizer))
    
    # 准备数据集
    ds = load_dataset("/workspace/dataset/datasets--HuggingFaceH4--ultrachat_200k/snapshots/8049631c405ae6576f93f445c6b8166f76f5505a/", split="train_sft")
    ds = ds.shuffle(seed=42).select(range(32))
    
    def preprocess(example):
        return {"text": tokenizer.apply_chat_template(example["messages"], tokenize=False)}
    
    def tokenize(sample):
        return tokenizer(sample["text"], padding=True, max_length=512, truncation=True, add_special_tokens=False)
    
    ds = ds.map(preprocess).map(tokenize, remove_columns=ds.column_names)
    
    # 3. 创建recipe
    recipe = [
        SmoothQuantModifier(smoothing_strength=0.8),
        GPTQModifier(targets="Linear", scheme="W8A8", ignore=["lm_head"]),
    ]
    
    print("\n🚀 开始执行oneshot量化...")
    print("="*80)
    
    # 4. 执行oneshot（这里会触发所有的追踪）
    try:
        quantized_model = traced_oneshot_func(
            model=model,
            dataset=ds,
            recipe=recipe,
            max_seq_length=512,
            num_calibration_samples=32,
        )
        print("\n✅ oneshot执行成功!")
        
    except Exception as e:
        print(f"\n❌ oneshot执行失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 5. 打印执行总结
    tracker.print_summary()
    
    # 6. 分析调用栈
    print("\n" + "="*80)
    print("关键代码位置分析")
    print("="*80)
    
    key_functions = [
        'oneshot', 'traced_oneshot_init', 'traced_oneshot_call', 
        'traced_apply_recipe', 'traced_initialize', 'traced_finalize',
        'traced_sq_init', 'traced_sq_apply', 'traced_gptq_init', 'traced_gptq_compress'
    ]
    
    for call in tracker.call_stack:
        if any(key in call['function'] for key in key_functions):
            print(f"🔑 {call['function']} @ {call['file']}:{call['line']}")
    
    return quantized_model

def verify_source_code_locations():
    """验证源码位置"""
    print("\n" + "="*80)
    print("验证源码位置")
    print("="*80)
    
    # 检查关键文件是否存在
    import os
    key_files = [
        "/workspace/lj_vllm_study/lib/python3.10/site-packages/llmcompressor/entrypoints/oneshot.py",
        "/workspace/lj_vllm_study/lib/python3.10/site-packages/llmcompressor/core/lifecycle.py",
        "/workspace/lj_vllm_study/lib/python3.10/site-packages/llmcompressor/modifiers/smoothquant/base.py",
        "/workspace/lj_vllm_study/lib/python3.10/site-packages/llmcompressor/modifiers/quantization/gptq/base.py",
    ]
    
    for file_path in key_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
    
    # 检查关键函数是否存在
    try:
        from llmcompressor.entrypoints.oneshot import oneshot, Oneshot
        from llmcompressor.core.lifecycle import CompressionLifecycle
        from llmcompressor.modifiers.smoothquant import SmoothQuantModifier
        from llmcompressor.modifiers.quantization.gptq import GPTQModifier
        
        print("✅ 所有关键类和函数导入成功")
        
        # 检查方法是否存在
        methods_to_check = [
            (Oneshot, '__init__'),
            (Oneshot, '__call__'),
            (Oneshot, 'apply_recipe_modifiers'),
            (CompressionLifecycle, 'initialize'),
            (CompressionLifecycle, 'finalize'),
            (SmoothQuantModifier, 'on_initialize'),
            (SmoothQuantModifier, '_apply_smoothing'),
            (GPTQModifier, 'on_initialize'),
            (GPTQModifier, 'compress_modules'),
        ]
        
        for cls, method_name in methods_to_check:
            if hasattr(cls, method_name):
                print(f"✅ {cls.__name__}.{method_name}")
            else:
                print(f"❌ {cls.__name__}.{method_name}")
                
    except ImportError as e:
        print(f"❌ 导入失败: {e}")

def main():
    """主函数"""
    print("🔍 Debug oneshot执行流程")
    print("基于实际源码的代码路径追踪")
    print("="*80)
    
    # 1. 验证源码位置
    verify_source_code_locations()
    
    # 2. 执行debug
    try:
        quantized_model = debug_oneshot_execution()
        print(f"\n🎉 Debug完成! 量化模型类型: {type(quantized_model)}")
    except Exception as e:
        print(f"\n💥 Debug失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
