#!/usr/bin/env python3
"""
生产环境SmoothQuant量化示例
基于详细分析结果的最佳实践实现
"""

import torch
import time
import psutil
import os
from transformers import AutoTokenizer, AutoModelForCausalLM
from datasets import load_dataset
from llmcompressor import oneshot
from llmcompressor.modifiers.quantization import GPTQModifier
from llmcompressor.modifiers.smoothquant import SmoothQuantModifier
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProductionSmoothQuantPipeline:
    """生产环境SmoothQuant量化流水线"""
    
    def __init__(self, model_path: str, output_dir: str):
        self.model_path = model_path
        self.output_dir = output_dir
        self.metrics = {}
        
    def validate_environment(self):
        """验证环境配置"""
        logger.info("🔍 验证环境配置...")
        
        # 检查PyTorch版本
        torch_version = torch.__version__
        logger.info(f"PyTorch版本: {torch_version}")
        
        if not torch_version.startswith('2.5'):
            logger.warning("⚠️ 建议使用PyTorch 2.5.1以避免FX tracing问题")
        
        # 检查GPU可用性
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            logger.info(f"可用GPU数量: {gpu_count}")
            for i in range(gpu_count):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                logger.info(f"GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
        else:
            logger.warning("⚠️ 未检测到GPU，将使用CPU进行量化")
        
        # 检查内存
        memory = psutil.virtual_memory()
        logger.info(f"系统内存: {memory.total / 1024**3:.1f}GB (可用: {memory.available / 1024**3:.1f}GB)")
        
        return True
    
    def load_model_and_tokenizer(self):
        """加载模型和分词器"""
        logger.info(f"📥 加载模型: {self.model_path}")
        
        start_time = time.time()
        
        # 加载模型
        model = AutoModelForCausalLM.from_pretrained(
            self.model_path,
            device_map="auto",
            torch_dtype="auto",
        )
        
        # 加载分词器
        tokenizer = AutoTokenizer.from_pretrained(self.model_path)
        
        # 处理pad_token
        if tokenizer.pad_token is None:
            tokenizer.add_special_tokens({'pad_token': '[PAD]'})
            model.resize_token_embeddings(len(tokenizer))
            logger.info("✅ 已添加pad_token")
        
        load_time = time.time() - start_time
        
        # 记录模型信息
        total_params = sum(p.numel() for p in model.parameters())
        model_size_mb = total_params * 2 / 1024**2  # FP16
        
        logger.info(f"✅ 模型加载完成 ({load_time:.2f}s)")
        logger.info(f"📊 模型参数: {total_params:,}")
        logger.info(f"📊 模型大小: {model_size_mb:.2f}MB")
        
        self.metrics['load_time'] = load_time
        self.metrics['total_params'] = total_params
        self.metrics['original_size_mb'] = model_size_mb
        
        return model, tokenizer
    
    def prepare_calibration_data(self, tokenizer, num_samples=512, max_length=512):
        """准备校准数据"""
        logger.info(f"📊 准备校准数据 (样本数: {num_samples})")
        
        # 使用内置数据集路径
        dataset_path = "/workspace/dataset/datasets--HuggingFaceH4--ultrachat_200k/snapshots/8049631c405ae6576f93f445c6b8166f76f5505a/"
        
        try:
            ds = load_dataset(dataset_path, split="train_sft")
            logger.info(f"✅ 数据集加载成功，总样本数: {len(ds)}")
        except Exception as e:
            logger.error(f"❌ 数据集加载失败: {e}")
            raise
        
        # 选择校准样本
        ds = ds.shuffle(seed=42).select(range(min(num_samples, len(ds))))
        
        # 预处理
        def preprocess(example):
            return {"text": tokenizer.apply_chat_template(example["messages"], tokenize=False)}
        
        ds = ds.map(preprocess)
        
        # Tokenization
        def tokenize(sample):
            return tokenizer(
                sample["text"],
                padding=True,
                max_length=max_length,
                truncation=True,
                add_special_tokens=False
            )
        
        ds = ds.map(tokenize, remove_columns=ds.column_names)
        
        logger.info(f"✅ 校准数据准备完成")
        
        return ds
    
    def create_quantization_recipe(self, smoothing_strength=0.8, scheme="W8A8"):
        """创建量化配置"""
        logger.info(f"⚙️ 创建量化配置 (平滑强度: {smoothing_strength}, 方案: {scheme})")
        
        recipe = [
            SmoothQuantModifier(smoothing_strength=smoothing_strength),
            GPTQModifier(targets="Linear", scheme=scheme, ignore=["lm_head"]),
        ]
        
        logger.info("✅ 量化配置创建完成")
        return recipe
    
    def perform_quantization(self, model, dataset, recipe, max_seq_length=512, num_calibration_samples=512):
        """执行量化"""
        logger.info("🚀 开始模型量化...")
        
        start_time = time.time()
        
        try:
            oneshot(
                model=model,
                dataset=dataset,
                recipe=recipe,
                max_seq_length=max_seq_length,
                num_calibration_samples=num_calibration_samples,
                output_dir=self.output_dir,  # 直接指定输出目录
            )
            
            quantization_time = time.time() - start_time
            logger.info(f"✅ 量化完成 ({quantization_time:.2f}s)")
            
            self.metrics['quantization_time'] = quantization_time
            
        except Exception as e:
            logger.error(f"❌ 量化失败: {e}")
            raise
        
        return model
    
    def save_quantized_model(self, model, tokenizer):
        """保存量化模型"""
        logger.info(f"💾 保存量化模型到: {self.output_dir}")
        
        try:
            # 如果oneshot没有自动保存，手动保存
            if not os.path.exists(self.output_dir):
                os.makedirs(self.output_dir, exist_ok=True)
                model.save_pretrained(self.output_dir, save_compressed=True)
                tokenizer.save_pretrained(self.output_dir)
            
            # 计算压缩后大小
            total_size = 0
            for root, dirs, files in os.walk(self.output_dir):
                for file in files:
                    total_size += os.path.getsize(os.path.join(root, file))
            
            compressed_size_mb = total_size / 1024**2
            compression_ratio = self.metrics['original_size_mb'] / compressed_size_mb
            
            logger.info(f"✅ 模型保存完成")
            logger.info(f"📊 压缩后大小: {compressed_size_mb:.2f}MB")
            logger.info(f"📊 压缩比: {compression_ratio:.2f}x")
            
            self.metrics['compressed_size_mb'] = compressed_size_mb
            self.metrics['compression_ratio'] = compression_ratio
            
        except Exception as e:
            logger.error(f"❌ 模型保存失败: {e}")
            raise
    
    def benchmark_model(self, model, tokenizer, test_inputs=None):
        """基准测试"""
        logger.info("🏃 执行基准测试...")
        
        if test_inputs is None:
            test_inputs = ["Hello, how are you?", "What is the capital of France?"]
        
        model.eval()
        
        # 预热
        with torch.no_grad():
            for _ in range(3):
                inputs = tokenizer("warmup", return_tensors="pt")
                if torch.cuda.is_available():
                    inputs = {k: v.cuda() for k, v in inputs.items()}
                _ = model(**inputs)
        
        # 测试推理速度
        inference_times = []
        
        for test_input in test_inputs:
            inputs = tokenizer(test_input, return_tensors="pt")
            if torch.cuda.is_available():
                inputs = {k: v.cuda() for k, v in inputs.items()}
            
            start_time = time.time()
            with torch.no_grad():
                outputs = model(**inputs)
            inference_time = time.time() - start_time
            inference_times.append(inference_time)
        
        avg_inference_time = sum(inference_times) / len(inference_times)
        
        logger.info(f"📊 平均推理时间: {avg_inference_time*1000:.2f}ms")
        
        self.metrics['avg_inference_time_ms'] = avg_inference_time * 1000
        
        return avg_inference_time
    
    def generate_report(self):
        """生成量化报告"""
        logger.info("📄 生成量化报告...")
        
        report = f"""
SmoothQuant量化报告
==================

模型信息:
- 原始参数数量: {self.metrics.get('total_params', 'N/A'):,}
- 原始模型大小: {self.metrics.get('original_size_mb', 'N/A'):.2f}MB
- 压缩后大小: {self.metrics.get('compressed_size_mb', 'N/A'):.2f}MB
- 压缩比: {self.metrics.get('compression_ratio', 'N/A'):.2f}x

性能指标:
- 模型加载时间: {self.metrics.get('load_time', 'N/A'):.2f}s
- 量化时间: {self.metrics.get('quantization_time', 'N/A'):.2f}s
- 平均推理时间: {self.metrics.get('avg_inference_time_ms', 'N/A'):.2f}ms

量化配置:
- 方法: SmoothQuant + GPTQ
- 精度: W8A8
- 平滑强度: 0.8
- 目标层: Linear (除lm_head)

环境信息:
- PyTorch版本: {torch.__version__}
- CUDA可用: {torch.cuda.is_available()}
- GPU数量: {torch.cuda.device_count() if torch.cuda.is_available() else 0}
"""
        
        report_path = os.path.join(self.output_dir, "quantization_report.txt")
        with open(report_path, "w", encoding="utf-8") as f:
            f.write(report)
        
        logger.info(f"✅ 报告已保存到: {report_path}")
        print(report)
        
        return report
    
    def run_complete_pipeline(self):
        """运行完整的量化流水线"""
        logger.info("🚀 启动完整量化流水线")
        
        try:
            # 1. 环境验证
            self.validate_environment()
            
            # 2. 加载模型
            model, tokenizer = self.load_model_and_tokenizer()
            
            # 3. 准备数据
            dataset = self.prepare_calibration_data(tokenizer)
            
            # 4. 创建配置
            recipe = self.create_quantization_recipe()
            
            # 5. 执行量化
            quantized_model = self.perform_quantization(model, dataset, recipe)
            
            # 6. 保存模型
            self.save_quantized_model(quantized_model, tokenizer)
            
            # 7. 基准测试
            self.benchmark_model(quantized_model, tokenizer)
            
            # 8. 生成报告
            self.generate_report()
            
            logger.info("🎉 量化流水线执行完成！")
            
        except Exception as e:
            logger.error(f"❌ 流水线执行失败: {e}")
            raise

def main():
    """主函数"""
    # 配置参数
    MODEL_PATH = "/home/<USER>/single_llama"
    OUTPUT_DIR = "single_llama-W8A8-SmoothQuant-Production"
    
    # 创建并运行流水线
    pipeline = ProductionSmoothQuantPipeline(MODEL_PATH, OUTPUT_DIR)
    pipeline.run_complete_pipeline()

if __name__ == "__main__":
    main()
